﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="responseMessage" Type="OutArgument(x:String)" />
    <x:Property Name="documentType" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="processID" Type="InArgument(x:String)" />
    <x:Property Name="PID" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SendToWidgetIDM_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="base64String" />
      <Variable x:TypeArguments="x:String" Name="fileName" />
      <Variable x:TypeArguments="x:String" Name="idmRequestString" />
      <Variable x:TypeArguments="njl:JToken" Name="idmRequestJtoken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="idmResponse" />
      <Variable x:TypeArguments="x:Int32" Name="idmResponseStatus" />
    </Sequence.Variables>
    <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64String]" ContinueOnError="True" DisplayName="SendToWidgetIDM_FileToBase64_2" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_1" />
    <Assign DisplayName="SendToWidgetIDM_Assign_fileName_3" sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[(documentPath.Substring(documentPath.LastIndexOf("\"c)+1,(documentPath.Length()-documentPath.LastIndexOf("\"c))-1)).Replace("'","").Replace("""","")]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToWidgetIDM_Template_Apply_4" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'item': { 'attrs': { 'attr': [ { 'name': 'processID', 'value': '{{%processID%}}' } ] }, 'resrs': { 'res': [ { 'filename': '{{%item%}}', 'base64':'{{%base64String%}}' } ] }, 'acl': { 'name': 'Public' }, 'entityName': '{{%documentType%}}' } }" Text="[idmRequestString]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>base64String</x:String>
            <x:String>item</x:String>
            <x:String>documentType</x:String>
            <x:String>processID</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>base64String</x:String>
            <x:String>fileName</x:String>
            <x:String>documentType</x:String>
            <x:String>processID</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToWidgetIDM_DeserializeJSON_5" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[idmRequestJtoken]" JTokenString="[idmRequestString]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SendToWidgetIDM_IONAPIRequestWizard_6" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[idmRequestJtoken.tostring]" Response="[idmResponse]" StatusCode="[idmResponseStatus]" Url="[tenantID+ &quot;IDM/api/items&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>$Language</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>en-US</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <Assign DisplayName="SendToWidgetIDM_Assign_PID_7" sap2010:WorkflowViewState.IdRef="Assign_6">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[PID]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[idmResponseStatus= 200]" DisplayName="SendToWidgetIDM_If_8" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <If Condition="[idmResponse.readastext.Contains(&quot;item&quot;)]" DisplayName="SendToWidgetIDM_If_9" sap2010:WorkflowViewState.IdRef="If_1">
          <If.Then>
            <Sequence DisplayName="SendToWidgetIDM_Sequence_10" sap2010:WorkflowViewState.IdRef="Sequence_3">
              <Assign DisplayName="SendToWidgetIDM_Assign_responseMessage_11" sap2010:WorkflowViewState.IdRef="Assign_2">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["Document added to RPAExceptionHandling IDM"]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="SendToWidgetIDM_Assign_PID_12" sap2010:WorkflowViewState.IdRef="Assign_5">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[PID]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[JToken.Parse(idmResponse.readastext)("item")("pid").ToString]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Then>
          <If.Else>
            <Assign DisplayName="SendToWidgetIDM_Assign_responseMessage_13" sap2010:WorkflowViewState.IdRef="Assign_3">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">["Failure while saving Document to RPAExceptionHandling IDM"]</InArgument>
              </Assign.Value>
            </Assign>
          </If.Else>
        </If>
      </If.Then>
      <If.Else>
        <Assign DisplayName="SendToWidgetIDM_Assign_responseMessage_14" sap2010:WorkflowViewState.IdRef="Assign_4">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">["Failure while saving Document to RPAExceptionHandling IDM"]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Else>
    </If>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToWidgetIDM_Append_Line_15" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[responseMessage]" Source="[logFile]" />
    <sads:DebugSymbol.Symbol>d1tDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFNlbmRUb1dpZGdldElETS54YW1sKUUDugEOAgEBTgVO3AECATpPBVYOAgEyVwVoGgIBLmkFad8BAgEpagV/HwIBIYABBYkBDgIBHYoBBbcBCgIBB7gBBbgBxwECAQJOM05DAgE9TpgBTqgBAgE7VDBUxgECATVRMVE7AgEzV58DV7MDAgEwV6MBV5kDAgEvacgBadwBAgEsaaYBaboBAgEqaoQCaqECAgEoaqsCaroCAgEmauACaocDAgEkasYCatsCAgEihgELhgE6AgEgggExggE2AgEeigETigEtAgEIjAEJqwEOAgEOrgEJtQESAgEKuAGeAbgBsQECAQW4AbkBuAHEAQIBA4wBF4wBTAIBD44BDZ8BGAIBFKIBDakBFgIBELMBNLMBcQIBDbABNbABRgIBC48BD5YBGAIBGZcBD54BGAIBFacBOKcBdQIBE6QBOaQBSgIBEZQBOpQBaAIBHJEBO5EBTAIBGpwBOpwBeAIBGJkBO5kBQAIBFg==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="FileToBase64_1" sap:VirtualizedContainerService.HintSize="800,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="800,62" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="800,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="800,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="800,22" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="800,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="532,442" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="800,596" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="800,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="822,1234">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="862,1354" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
