﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="SupplierNo" Type="InArgument(x:String)" />
    <x:Property Name="ivdate" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="sino" Type="InArgument(x:String)" />
    <x:Property Name="cucd" Type="InArgument(x:String)" />
    <x:Property Name="tepy" Type="InArgument(x:String)" />
    <x:Property Name="pyme" Type="InArgument(x:String)" />
    <x:Property Name="cuam" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="OutArgument(x:String)" />
    <x:Property Name="bkid" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="InArgument(x:String)" />
    <x:Property Name="discountTerms" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="txap" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="AddHead_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_5">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
      <Variable x:TypeArguments="njl:JToken" Name="out5" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
    </Sequence.Variables>
    <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" DisplayName="AddHead_If_2" sap2010:WorkflowViewState.IdRef="If_5">
      <If.Then>
        <Sequence DisplayName="AddHead_Sequence_3" sap2010:WorkflowViewState.IdRef="Sequence_6">
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SUNO</x:String>
                  <x:String>IVDT</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>CUCD</x:String>
                  <x:String>TEPY</x:String>
                  <x:String>PYME</x:String>
                  <x:String>CUAM</x:String>
                  <x:String>IMCD</x:String>
                  <x:String>CRTP</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>format</x:String>
                  <x:String>extendedresult</x:String>
                  <x:String>APCD</x:String>
                  <x:String>CORI</x:String>
                  <x:String>TECD</x:String>
                  <x:String>TXAP</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SupplierNo</x:String>
                  <x:String>ivdate</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>cucd</x:String>
                  <x:String>tepy</x:String>
                  <x:String>pyme</x:String>
                  <x:String>cuam</x:String>
                  <x:String>1</x:String>
                  <x:String>1</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>false</x:String>
                  <x:String>true</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>false</x:String>
                  <x:String>authUser</x:String>
                  <x:String>correlationID</x:String>
                  <x:String>discountTerms</x:String>
                  <x:String>txap</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_5" sap2010:WorkflowViewState.IdRef="If_6">
            <If.Then>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="32">
                      <x:String>SUNO</x:String>
                      <x:String>IVDT</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>SINO</x:String>
                      <x:String>CUCD</x:String>
                      <x:String>TEPY</x:String>
                      <x:String>PYME</x:String>
                      <x:String>CUAM</x:String>
                      <x:String>IMCD</x:String>
                      <x:String>CRTP</x:String>
                      <x:String>dateformat</x:String>
                      <x:String>excludeempty</x:String>
                      <x:String>righttrim</x:String>
                      <x:String>format</x:String>
                      <x:String>extendedresult</x:String>
                      <x:String>APCD</x:String>
                      <x:String>BKID</x:String>
                      <x:String>CORI</x:String>
                      <x:String>TECD</x:String>
                      <x:String>TXAP</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="32">
                      <x:String>SupplierNo</x:String>
                      <x:String>ivdate</x:String>
                      <x:String>division</x:String>
                      <x:String>sino</x:String>
                      <x:String>cucd</x:String>
                      <x:String>tepy</x:String>
                      <x:String>pyme</x:String>
                      <x:String>cuam</x:String>
                      <x:String>1</x:String>
                      <x:String>1</x:String>
                      <x:String>YMD8</x:String>
                      <x:String>false</x:String>
                      <x:String>true</x:String>
                      <x:String>PRETTY</x:String>
                      <x:String>false</x:String>
                      <x:String>authUser</x:String>
                      <x:String>bkid</x:String>
                      <x:String>correlationID</x:String>
                      <x:String>discountTerms</x:String>
                      <x:String>txap</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="AddHead_Sequence_7" sap2010:WorkflowViewState.IdRef="Sequence_7">
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_8" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SUNO</x:String>
                  <x:String>IVDT</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>CUCD</x:String>
                  <x:String>TEPY</x:String>
                  <x:String>PYME</x:String>
                  <x:String>CUAM</x:String>
                  <x:String>IMCD</x:String>
                  <x:String>CRTP</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>format</x:String>
                  <x:String>extendedresult</x:String>
                  <x:String>APCD</x:String>
                  <x:String>CORI</x:String>
                  <x:String>TXAP</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="32">
                  <x:String>SupplierNo</x:String>
                  <x:String>ivdate</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>cucd</x:String>
                  <x:String>tepy</x:String>
                  <x:String>pyme</x:String>
                  <x:String>cuam</x:String>
                  <x:String>1</x:String>
                  <x:String>1</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>false</x:String>
                  <x:String>true</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>false</x:String>
                  <x:String>authUser</x:String>
                  <x:String>correlationID</x:String>
                  <x:String>txap</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_9" sap2010:WorkflowViewState.IdRef="If_7">
            <If.Then>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHead_IONAPIRequestWizard_10" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="32">
                      <x:String>SUNO</x:String>
                      <x:String>IVDT</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>SINO</x:String>
                      <x:String>CUCD</x:String>
                      <x:String>TEPY</x:String>
                      <x:String>PYME</x:String>
                      <x:String>CUAM</x:String>
                      <x:String>IMCD</x:String>
                      <x:String>CRTP</x:String>
                      <x:String>dateformat</x:String>
                      <x:String>excludeempty</x:String>
                      <x:String>righttrim</x:String>
                      <x:String>format</x:String>
                      <x:String>extendedresult</x:String>
                      <x:String>APCD</x:String>
                      <x:String>BKID</x:String>
                      <x:String>CORI</x:String>
                      <x:String>TXAP</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="32">
                      <x:String>SupplierNo</x:String>
                      <x:String>ivdate</x:String>
                      <x:String>division</x:String>
                      <x:String>sino</x:String>
                      <x:String>cucd</x:String>
                      <x:String>tepy</x:String>
                      <x:String>pyme</x:String>
                      <x:String>cuam</x:String>
                      <x:String>1</x:String>
                      <x:String>1</x:String>
                      <x:String>YMD8</x:String>
                      <x:String>false</x:String>
                      <x:String>true</x:String>
                      <x:String>PRETTY</x:String>
                      <x:String>false</x:String>
                      <x:String>authUser</x:String>
                      <x:String>bkid</x:String>
                      <x:String>correlationID</x:String>
                      <x:String>txap</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Then>
          </If>
        </Sequence>
      </If.Else>
    </If>
    <If Condition="[StatusCode5 = 200]" DisplayName="AddHead_If_11" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Sequence DisplayName="AddHead_Sequence_12" sap2010:WorkflowViewState.IdRef="Sequence_3">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Int32" Name="respout1" />
            <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
            <Variable x:TypeArguments="x:Int32" Name="m" />
          </Sequence.Variables>
          <Assign DisplayName="AddHead_Assign_out5_13" sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="AddHead_If_14" sap2010:WorkflowViewState.IdRef="If_2">
            <If.Then>
              <Sequence DisplayName="AddHead_Sequence_15" sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Assign DisplayName="AddHead_Assign_InvoiceAlreadyExists_16" sap2010:WorkflowViewState.IdRef="Assign_2">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddHead_Assign_inbnValue_17" sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddHead_Assign_commentStatus_18" sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="AddHead_If_19" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <Assign DisplayName="AddHead_Assign_Status_20" sap2010:WorkflowViewState.IdRef="Assign_11">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                  <If.Else>
                    <Assign DisplayName="AddHead_Assign_Status_21" sap2010:WorkflowViewState.IdRef="Assign_5">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="AddHead_Sequence_22" sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign DisplayName="AddHead_Assign_InvoiceAlreadyExists_23" sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddHead_Assign_inbnValue_24" sap2010:WorkflowViewState.IdRef="Assign_7">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddHead_Assign_commentStatus_25" sap2010:WorkflowViewState.IdRef="Assign_8">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddHead_Append_Line_26" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="AddHead_Sequence_27" sap2010:WorkflowViewState.IdRef="Sequence_4">
          <Assign DisplayName="AddHead_Assign_commentStatus_28" sap2010:WorkflowViewState.IdRef="Assign_9">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddHead_Assign_Status_29" sap2010:WorkflowViewState.IdRef="Assign_10">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddHead_Append_Line_30" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1JDOlxVc2Vyc1xtcGF0aGFrb3RhXEFwcERhdGFcTG9jYWxcSW5mb3JSUEFcTTNJbnZvaWNlUHJvY2Vzc2luZ0dlbkFJVjNcQWRkSGVhZC54YW1sSmgD5QMOAgEBbwXoAgoCAUPpAgXjAwoCAQJvE29lAgFEcQnsARQCAVfvAQnmAhQCAUbpAhPpAigCAQPrAgnMAxQCARPPAwnhAxQCAQVyC6sBJQIBYawBC+sBEAIBWPABC6cCJQIBUKgCC+UCEAIBR/ECC/gCFAIBP/kCC8oDEAIBGcsDC8sDvgECARTQAwvXAxQCAQ/YAwvfAxQCAQvgAwvgA74BAgEGcqMCcq8CAgFmcs8CcpMDAgFkcrsCcsoCAgFirAEZrAGDAQIBWa4BD+kBKQIBWvABowLwAa8CAgFV8AHPAvABkwMCAVPwAbsC8AHKAgIBUagCGagCgwECAUiqAg/jAikCAUn2Ajj2AlsCAULzAjnzAj8CAUD5Ahn5AmgCARr7Ag+sAxoCASivAw/IAxoCARvLA5cBywOoAQIBF8sDsAHLA7sBAgEV1QM21QNvAgES0gM30gNGAgEQ3QM23QNBAgEO2gM32gM/AgEM4AOXAeADqAECAQngA7AB4AO7AQIBB64BpwKuAbMCAgFfrgHTAq4BlwMCAV2uAb8CrgHOAgIBW6oCpwKqArMCAgFOqgLTAqoClwMCAUyqAr8CqgLOAgIBSvwCEYMDGgIBO4QDEY0DGgIBN44DEZUDGgIBM5YDEasDFgIBKbADEbcDGgIBJLgDEb8DGgIBIMADEccDGgIBHIEDPYEDQQIBPv4CPv4CVAIBPIoDF4oDRgIBOoYDPYYDSAIBOJMDPJMDawIBNpADPZADTAIBNJYDH5YDcgIBKpgDFZ8DHgIBL6IDFakDHgIBK7UDPbUDQgIBJ7IDPrIDVAIBJb0DPL0DcQIBI7oDPboDSAIBIcUDPMUDVgIBH8IDPcIDTAIBHZ0DQJ0DUQIBMpoDQZoDSQIBMKcDQKcDRwIBLqQDQaQDSQIBLA==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="486,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="486,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="1134,554" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="822,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="532,646">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="822,800" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="822,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="844,1088">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1134,1242" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1156,1960">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1196,2240" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
