﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="M3TotalTableRows" Type="OutArgument(scg:List(s:String[]))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="req2" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="VendorID" Type="OutArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="DNinM3exists" Type="OutArgument(x:Boolean)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="x:String" Name="pono" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="LinesExtractionWithPO_IONAPIRequestWizard_1" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
            <x:String>SEPC</x:String>
            <x:String>maxrecs</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req2</x:String>
            <x:String>~</x:String>
            <x:String>1000</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <Switch x:TypeArguments="x:Boolean" Expression="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_3">
      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_8">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
          <Assign.To>
            <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
          </Assign.Value>
        </Assign>
        <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">PO lines not received</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Then>
          <If.Else>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:Int32" Name="x" />
                <Variable x:TypeArguments="s:String[]" Name="M3Values" />
                <Variable x:TypeArguments="x:String" Name="cono" />
              </Sequence.Variables>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="njl:JToken" DisplayName="LinesExtractionWithPO_ForEach_2" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                <ActivityAction x:TypeArguments="njl:JToken">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                      <Assign.To>
                        <OutArgument x:TypeArguments="s:String[]">[M3Values]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="s:String[]">[New String(11) {}]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(14).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(17).ToString())).ToString()]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iad:CommentOut DisplayName="LinesExtractionWithPO_CommentOut_3" sap2010:WorkflowViewState.IdRef="CommentOut_5">
                      <iad:CommentOut.Activities>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[m3Values(3).Contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_17">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[m3Values(3).SubString(0,m3Values(3).IndexOf("."))]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                      </iad:CommentOut.Activities>
                    </iad:CommentOut>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Switch x:TypeArguments="x:Boolean" Expression="[CInt(m3Values(3)) &gt; 0]" sap2010:WorkflowViewState.IdRef="Switch`1_2">
                      <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_24">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Decimal" Name="calQty" />
                          <Variable x:TypeArguments="x:Decimal" Name="calPrice" />
                          <Variable x:TypeArguments="x:String" Name="req3" />
                          <Variable x:TypeArguments="x:Double" Name="tot" />
                          <Variable x:TypeArguments="x:Double" Name="totqty" />
                        </Sequence.Variables>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_4" sap2010:WorkflowViewState.IdRef="Assign_71">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(0)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(0).ToString() + "99"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_5" sap2010:WorkflowViewState.IdRef="Assign_72">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(1)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_6" sap2010:WorkflowViewState.IdRef="Assign_73">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                          <TryCatch.Try>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Math.Round((((Convert.ToDecimal(item("REPL").ToString().split("~"C)(13)) -Convert.ToDecimal(item("REPL").ToString().split("~"C)(18)))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(16))))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(2)))),4).ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </TryCatch.Try>
                          <TryCatch.Catches>
                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                              <ActivityAction x:TypeArguments="s:Exception">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                </ActivityAction.Argument>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(14).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(17).ToString())).ToString()]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </ActivityAction>
                            </Catch>
                          </TryCatch.Catches>
                        </TryCatch>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_7" sap2010:WorkflowViewState.IdRef="Assign_76">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(4)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_8" sap2010:WorkflowViewState.IdRef="Assign_77">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(5)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_9" sap2010:WorkflowViewState.IdRef="Assign_78">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(6)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(11).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad:CommentOut DisplayName="LinesExtractionWithPO_CommentOut_10" sap2010:WorkflowViewState.IdRef="CommentOut_6">
                          <iad:CommentOut.Activities>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Math.Round((((Convert.ToDecimal(item("REPL").ToString().split("~"C)(13)) -Convert.ToDecimal(item("REPL").ToString().split("~"C)(18)))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(16))))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(2)))),4).ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(item("REPL").ToString().split("~"C)(3)) -Convert.ToDecimal(item("REPL").ToString().split("~"C)(10))).ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_11" sap2010:WorkflowViewState.IdRef="Assign_81">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(7)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_12" sap2010:WorkflowViewState.IdRef="Assign_82">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(8)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(8).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_13" sap2010:WorkflowViewState.IdRef="Assign_83">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(9)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(6).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="LinesExtractionWithPO_Assign_m3Values_14" sap2010:WorkflowViewState.IdRef="Assign_84">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(10)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(3).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(10).ToString())).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad:CommentOut DisplayName="LinesExtractionWithPO_CommentOut_15" sap2010:WorkflowViewState.IdRef="CommentOut_7">
                          <iad:CommentOut.Activities>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_85">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[req3]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["F3RCAC,F3CDSE,F3CEID from FGRPCL where F3PUNO = " + m3Values(6) +" and F3SCOC != 0.000000 and F3PNLI = " + m3Values(0) + " and F3DIVI = " + m3Values(8)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="LinesExtractionWithPO_IONAPIRequestWizard_16" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>QERY</x:String>
                                      <x:String>SEPC</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>req3</x:String>
                                      <x:String>~</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_19">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                    </Sequence.Variables>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_86">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_18">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                                          <ForEach x:TypeArguments="njl:JToken" DisplayName="LinesExtractionWithPO_ForEach_17" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                            <ActivityAction x:TypeArguments="njl:JToken">
                                              <ActivityAction.Argument>
                                                <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                              </ActivityAction.Argument>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Double">[tot]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Double">[tot + Convert.toDecimal(item("REPL").ToString.split("~"C)(0))]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </ActivityAction>
                                          </ForEach>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + m3Values(6)+"."]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="LinesExtractionWithPO_Append_Line_18" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                        </InvokeMethod>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </Switch>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </Sequence>
          </If.Else>
        </If>
      </Sequence>
      <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_9">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines for the PO " + pono +"."]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="LinesExtractionWithPO_Append_Line_19" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
      </Sequence>
    </Switch>
    <sads:DebugSymbol.Symbol>d2FDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXExpbmVzRXh0cmFjdGlvbldpdGhQTy54YW1saE0DgQQOAgEBVAVbDgMBiQFcBXUfAwGCAXYF/wMOAgECWTFZNgMBjAFWMlZAAwGKAVyaAlymAgMBhwFcxgJc+QMDAYUBXLICXMECAwGDAXY0dkkCAQN3B+sDEgIBFOwDB/4DEgIBBXgJfxICAX6AAQmHARICAXqIAQnqAw4CARXtAwn0AxICAQ/1Awn8AxICAQv9Awn9A7wBAgEGfTZ9WQMBgQF6N3o9AgF/hQFAhQFXAgF9ggFBggFTAgF7iAEXiAFmAgEWigENmwEYAgFxngEN6AMYAgEX8gM08gN9AgES7wM17wNEAgEQ+gM0+gM/AgEO9wM19wM9AgEM/QOVAf0DpgECAQn9A64B/QO5AQIBB4sBD5IBGAIBdpMBD5oBGAIBcqQBD6sBGAIBbawBD+cDGQIBGJABOpABTwIBeY0BO40BSgIBd5gBOpgBRwIBdZUBO5UBQwIBc6kBOakBOgIBcKYBOqYBPQIBbqwBjgGsAcMBAgFssQET5QMeAgEZsgEVuQEeAgFougEVwQEeAgFkwgEVyQEeAgFgygEV4QEmAgFf4gEV6QEeAgFb6gEV5AMeAgEatwFBtwFFAgFrtAFCtAFQAgFpvwFCvwFVAgFnvAFDvAFNAgFlxwFAxwHcAQIBY8QBQcQBTgIBYecBQOcBcwIBXuQBQeQBSwIBXOoBROoBYAIBG+sBF+MDIgIBHfMBGfoBIgIBV/sBGYICIgIBU4MCGYoCIgIBT4sCGacCJAIBRqgCGa8CIgIBQrACGbcCIgIBPrgCGb8CIgIBOsACGdMCKgIBOdQCGdsCIgIBNdwCGeMCIgIBMeQCGesCIgIBLewCGfMCIgIBKfQCGdQDKgIBKNUDGdoDKAIBI9sDGeIDIgIBHvgBRPgBfgIBWvUBRfUBUgIBWIACRIACdwIBVv0BRf0BUgIBVIgCRIgCdwIBUoUCRYUCUgIBUI0CHZQCJgIBS5wCIaMCKgIBR60CRK0CdwIBRaoCRaoCUgIBQ7UCRLUCdwIBQbICRbICUgIBP70CRL0CeAIBPboCRboCUgIBO9kCRNkCdwIBONYCRdYCUgIBNuECROECdwIBNN4CRd4CUgIBMukCROkCdwIBMOYCReYCUgIBLvECRPEC3wECASzuAkXuAlMCASrXA1DXA2ICASbZA0TZA04CASTgA0PgA0gCASHdA0TdA0cCAR+SAkiSAtYCAgFOjwJJjwJWAgFMoQJMoQLoAQIBSp4CTZ4CWgIBSA==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="476,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="476,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="840,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="840,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="528.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="476,62" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="476,62" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="476,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="CommentOut_5" sap:VirtualizedContainerService.HintSize="476,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="476,60" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="419,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_6" sap:VirtualizedContainerService.HintSize="419,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="419,60" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="284.666666666667,214.666666666667" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="306.666666666667,338.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="464,492.666666666667" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="486,718.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="776,872.666666666667" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="798,1160.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_7" sap:VirtualizedContainerService.HintSize="418.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="418.666666666667,134" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="441,1885">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_2" sap:VirtualizedContainerService.HintSize="476,2069">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="498,2697.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="528.666666666667,2850" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="550.666666666667,3076">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="840,3224">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="862,3499">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_3" sap:VirtualizedContainerService.HintSize="476,160" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="498,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="538,566" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
