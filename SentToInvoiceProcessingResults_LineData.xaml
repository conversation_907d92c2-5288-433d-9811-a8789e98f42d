﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="logicalId" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="Process_ID" Type="InArgument(x:String)" />
    <x:Property Name="Operation" Type="InArgument(x:String)" />
    <x:Property Name="documentName" Type="InArgument(x:String)" />
    <x:Property Name="AttributeList" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="x:Int32" Name="responseStatus" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JToken" Name="jout1" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="responseObject" />
      <Variable x:TypeArguments="x:String" Name="requestStr" />
      <Variable x:TypeArguments="njl:JToken" Name="requestToken" />
      <Variable x:TypeArguments="x:String" Name="varJtoken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
      <Variable x:TypeArguments="njl:JToken" Name="uout" />
      <Variable x:TypeArguments="x:String" Name="ERP" />
      <Variable x:TypeArguments="x:String" Name="createdBy" />
      <Variable x:TypeArguments="x:String" Name="userID" />
      <Variable x:TypeArguments="x:String" Name="LineID" />
      <Variable x:TypeArguments="x:Int32" Name="Variation_ID" />
      <Variable x:TypeArguments="x:Int32" Name="Index" />
      <Variable x:TypeArguments="x:String" Name="AttributeData" />
      <Variable x:TypeArguments="x:String" Name="Var" />
    </Sequence.Variables>
    <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_AttributeData_2" sap2010:WorkflowViewState.IdRef="Assign_52">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[AttributeData]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_Index_3" sap2010:WorkflowViewState.IdRef="Assign_37">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[Index]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">1</InArgument>
      </Assign.Value>
    </Assign>
    <ForEach x:TypeArguments="s:String[]" DisplayName="SentToInvoiceProcessingResults_LineData_ForEach_4" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[AttributeList]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
        </ActivityAction.Argument>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
          <If Condition="[Operation = &quot;insert&quot;]" DisplayName="SentToInvoiceProcessingResults_LineData_If_5" sap2010:WorkflowViewState.IdRef="If_11">
            <If.Then>
              <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_6" sap2010:WorkflowViewState.IdRef="Sequence_25">
                <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_Variation_ID_7" sap2010:WorkflowViewState.IdRef="Assign_36">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[Variation_ID]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">1</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_LineID_8" sap2010:WorkflowViewState.IdRef="Assign_38">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[LineID]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Process_ID+Convert.toString(Index)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_AttributeData_9" sap2010:WorkflowViewState.IdRef="Assign_39">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AttributeData]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[AttributeData + item(1).Replace("""","") + "\\n"]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_Var_10" sap2010:WorkflowViewState.IdRef="Assign_40">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Var]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Convert.toString(Variation_ID)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_index_11" sap2010:WorkflowViewState.IdRef="Assign_47">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[index]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">[index+1]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_12" sap2010:WorkflowViewState.IdRef="Sequence_26">
                <If Condition="[Operation = &quot;update&quot;]" DisplayName="SentToInvoiceProcessingResults_LineData_If_13" sap2010:WorkflowViewState.IdRef="If_12">
                  <If.Then>
                    <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_14" sap2010:WorkflowViewState.IdRef="Sequence_27">
                      <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_LineID_15" sap2010:WorkflowViewState.IdRef="Assign_42">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[LineID]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[item(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_AttributeData_16" sap2010:WorkflowViewState.IdRef="Assign_43">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AttributeData]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[item(1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_Var_17" sap2010:WorkflowViewState.IdRef="Assign_46">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Var]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(Convert.ToDouble(item(2))+1).tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_18" sap2010:WorkflowViewState.IdRef="Sequence_38">
      <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_AttributeData_19" sap2010:WorkflowViewState.IdRef="Assign_53">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AttributeData]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[AttributeData.SubString(0,AttributeData.Length-3)]</InArgument>
        </Assign.Value>
      </Assign>
      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_LineData_Template_Apply_20" sap2010:WorkflowViewState.IdRef="Template_Apply_11" Template="[&quot;{\&quot;&quot;Line_ID\&quot;&quot;:\&quot;&quot;{{%LineID%}}\&quot;&quot;,\&quot;&quot;RPA_Process_ID\&quot;&quot;:\&quot;&quot;{{%Process_ID%}}\&quot;&quot;,\&quot;&quot;Data\&quot;&quot;:\&quot;&quot;{{%AttributeData%}}\&quot;&quot;,\&quot;&quot;Variation_ID\&quot;&quot;:\&quot;&quot;{{%Variation_ID%}}\&quot;&quot;}&quot;]" Text="[value]">
        <ias:Template_Apply.Values>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>LineID</x:String>
              <x:String>Process_ID</x:String>
              <x:String>AttributeData</x:String>
              <x:String>Variation_ID</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>LineID</x:String>
              <x:String>Process_ID</x:String>
              <x:String>AttributeData</x:String>
              <x:String>Var</x:String>
            </scg:List>
          </scg:List>
        </ias:Template_Apply.Values>
      </ias:Template_Apply>
      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_LineData_Template_Apply_21" sap2010:WorkflowViewState.IdRef="Template_Apply_12" Template="[&quot;{  &quot;&quot;documentName&quot;&quot;: &quot;&quot;{{%documentName%}}&quot;&quot;,  &quot;&quot;messageId&quot;&quot;: &quot;&quot;msg#1234598&quot;&quot;, &quot;&quot;fromLogicalId&quot;&quot;: &quot;&quot;lid://{{%logicalId%}}&quot;&quot;, &quot;&quot;toLogicalId&quot;&quot;: &quot;&quot;lid://default&quot;&quot;,  &quot;&quot;document&quot;&quot;: {     &quot;&quot;value&quot;&quot;: &quot;&quot;{{%datalakeAttributes%}}&quot;&quot;,  &quot;&quot;encoding&quot;&quot;: &quot;&quot;NONE&quot;&quot;,  &quot;&quot;characterSet&quot;&quot;: &quot;&quot;UTF-8&quot;&quot; } }&quot;]" Text="[requestStr]">
        <ias:Template_Apply.Values>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>documentName</x:String>
              <x:String>logicalId</x:String>
              <x:String>datalakeAttributes</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>documentName</x:String>
              <x:String>logicalId</x:String>
              <x:String>value</x:String>
            </scg:List>
          </scg:List>
        </ias:Template_Apply.Values>
      </ias:Template_Apply>
      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_LineData_DeserializeJSON_22" sap2010:WorkflowViewState.IdRef="DeserializeJSON_6" JTokenObject="[jout]" JTokenString="[requestStr]" />
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_LineData_IONAPIRequestWizard_23" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" PostData="[requestStr]" Response="[responseObject]" ResponseCode="[responseStatus]" Url="[imsAPIUrl]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
      </iai:IONAPIRequestWizard>
      <If Condition="[responseStatus= 200]" DisplayName="SentToInvoiceProcessingResults_LineData_If_24" sap2010:WorkflowViewState.IdRef="If_18">
        <If.Then>
          <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_25" sap2010:WorkflowViewState.IdRef="Sequence_37">
            <Sequence.Variables>
              <Variable x:TypeArguments="njl:JToken" Name="reponseToken" />
            </Sequence.Variables>
            <Assign DisplayName="SentToInvoiceProcessingResults_LineData_Assign_reponseToken_26" sap2010:WorkflowViewState.IdRef="Assign_51">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[reponseToken]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[responseObject.readasjson]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[reponseToken.tostring.Contains(&quot;status&quot;) and reponseToken(&quot;status&quot;).tostring=&quot;OK&quot;]" DisplayName="SentToInvoiceProcessingResults_LineData_If_27" sap2010:WorkflowViewState.IdRef="If_17">
              <If.Then>
                <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_28" sap2010:WorkflowViewState.IdRef="Sequence_35">
                  <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_LineData_StudioWriteLine_29" sap2010:WorkflowViewState.IdRef="StudioWriteLine_3" Line="Datalake Status : OK" />
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence DisplayName="SentToInvoiceProcessingResults_LineData_Sequence_30" sap2010:WorkflowViewState.IdRef="Sequence_36">
                  <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToInvoiceProcessingResults_LineData_StudioWriteLine_31" sap2010:WorkflowViewState.IdRef="StudioWriteLine_4" Line="Datalake Status : ERROR" />
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>d3NDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFNlbnRUb0ludm9pY2VQcm9jZXNzaW5nUmVzdWx0c19MaW5lRGF0YS54YW1sSUgDnwIOAgEBXQVmDgIBZWcFbg4CAWFvBccBDwIBL8gBBZ0CEAIBAmMLYzoCAWhfMV9AAgFmbC9sMAIBZGkwaTcCAWJvgwFvlAECAV90CcUBFAIBMMkBB9ABEAIBKdEBB+IBHAIBJeMBB/IBHAIBIfMBB/MBzwECARz0AQf/ASECAROAAgecAgwCAQN1C8QBEAIBMc4BMs4BZQIBLMsBM8sBQgIBKtEBgQTRAYoEAgEn0QGsAdEB+wMCASbjAZMG4wGhBgIBI+MBugHjAY0GAgEi8wG+AfMBzAECAR/zAagB8wGwAQIBHfQBnAL0AaoCAgEa9AHUAvQB5gICARj0AbQC9AHGAgIBFvQB6wL0AfgCAgEUgAIVgAIsAgEEggILmgIWAgEGdRl1OwIBMncPoAEaAgFHowEPwgEaAgE0hgINjQIWAgEPjgINmQISAgEHeBF/GgIBW4ABEYcBGgIBV4gBEY8BGgIBUZABEZcBGgIBTZgBEZ8BGgIBSKQBEcEBFgIBNYsCOosCVQIBEogCO4gCSQIBEI4CG44CjgECAQiQAhGSAhwCAQyVAhGXAhwCAQl9O308AgFeejx6SgIBXIUBPIUBYAIBWoIBPYIBRQIBWI0BPI0BbgIBVIoBPYoBTAIBUpUBPJUBXAIBUJIBPZIBQgIBTp0BO50BRAIBS5oBPJoBQwIBSaQBH6QBQQIBNqYBFb8BIAIBOJECE5ECvwECAQ2WAhOWAsIBAgEKpwEXrgEgAgFCrwEXtgEgAgE9twEXvgEgAgE5kQKmAZECvAECAQ6WAqYBlgK/AQIBC6wBQqwBSwIBRakBQ6kBSwIBQ7QBQrQBSwIBQLEBQ7EBUgIBPrwBQrwBagIBPLkBQ7kBSAIBOg==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="828.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="828.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="264,594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="464,544" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="486,668">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="776,822" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="798,946">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="828.666666666667,1098.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="618,62" />
      <sap2010:ViewStateData Id="Template_Apply_11" sap:VirtualizedContainerService.HintSize="618,22" />
      <sap2010:ViewStateData Id="Template_Apply_12" sap:VirtualizedContainerService.HintSize="618,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_6" sap:VirtualizedContainerService.HintSize="618,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="618,22" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="470,62" />
      <sap2010:ViewStateData Id="StudioWriteLine_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="StudioWriteLine_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="470,300" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="492,526">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="618,680" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="828.666666666667,1154">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="850.666666666667,2620.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="890.666666666667,2740.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
