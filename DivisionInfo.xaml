﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="divisionInfo" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="divisionTextInput" />
      <Variable x:TypeArguments="njl:JToken" Name="divisionjsoninput" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="divisionresponseobj" />
      <Variable x:TypeArguments="s:String[]" Name="vendorResp1" />
      <Variable x:TypeArguments="njl:JArray" Name="divJarray" />
      <Variable x:TypeArguments="njl:JToken" Name="divJtoken" />
      <Variable x:TypeArguments="x:Int32" Name="responsecode" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[""]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[""]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[divisioninfo]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[divisioninfo.replace(",","")]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="DivisionInfo_Template_Apply_1" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'data':[['{{%divisionInfo%}}']]}" Text="[divisionTextInput]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>divisionInfo</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>divisionInfo</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="DivisionInfo_DeserializeJSON_2" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[divisionjsoninput]" JTokenString="[divisionTextInput]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="DivisionInfo_IONAPIRequestWizard_3" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[divisionjsoninput.tostring]" Response="[divisionresponseobj]" ResponseCode="[responsecode]" Url="[tenantID + &quot;COLEMANAI/aiplatform/v1/endpoints/AddressSimilarity_Quest_M3/prediction&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <If Condition="[responsecode=200 or responsecode=201 or responsecode=202]" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[divJtoken]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[Jtoken.parse(divisionresponseobj.ReadAstext)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JArray">[divJarray]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JArray">[directcast(divJtoken,Jarray)]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[divJarray.toString&lt;&gt;&quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
              <If.Then>
                <If Condition="[divJarray(0)(0).ToString &lt;&gt; &quot;No_Similarity&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[divJarray(0)(1).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[divJarray(0)(0).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[""]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[""]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[""]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[""]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[""]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[""]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1hDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXERpdmlzaW9uSW5mby54YW1sP0UD7wEOAgEBTwVWDgIBU1cFXg4CAU9fBWYOAgFKZwVyGgIBRnMFc+EBAgFBdAV/HwIBOYABBe0BCgIBAlQwVDQCAVZRMVE7AgFUXDBcNAIBUlkxWToCAVBkMGROAgFNYTFhPwIBS2fOAWfjAQIBSGejAWfIAQIBR3PJAXPeAQIBRHOmAXO7AQIBQnScAnS6AgIBQHTpAnT5AgIBPnTEAnTbAgIBPHT+AnTgAwIBOoABE4ABTwIBA4IBCdcBFAIBENoBCesBFAIBB4MBC9YBFgIBEdsBC+IBFAIBDOMBC+oBFAIBCIQBDYsBFgIBNYwBDZMBFgIBMZQBDdUBEgIBEuABNuABOgIBD90BN90BQQIBDegBNugBOgIBC+UBN+UBQAIBCYkBOokBaAIBOIYBO4YBRgIBNpEBOpEBWAIBNI4BO44BRgIBMpQBG5QBRwIBE5YBEb8BFgIBHcIBEdMBHAIBFJYBH5YBXgIBHpgBFakBIAIBKKwBFb0BIAIBH8MBE8oBHAIBGcsBE9IBHAIBFZkBF6ABIAIBLaEBF6gBIAIBKa0BF7QBIAIBJLUBF7wBIAIBIMgBPsgBQgIBHMUBP8UBSQIBGtABPtABQgIBGM0BP80BSAIBFp4BQp4BXAIBMJsBQ5sBTQIBLqYBQqYBXAIBLKMBQ6MBTAIBKrIBQrIBRgIBJ68BQ68BTQIBJboBQroBRgIBI7cBQ7cBTAIBIQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="1175,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="1175,60" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1175,60" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="1175,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="1175,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="1175,22" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="842,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="842,60" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="553,432" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="842,580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="864,904">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="886,1028">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1175,1176" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1197,1786">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1237,1866" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
