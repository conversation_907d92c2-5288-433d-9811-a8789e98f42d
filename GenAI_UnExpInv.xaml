﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iad1="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt" Type="InArgument(x:String)" />
    <x:Property Name="values" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="OutArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.IO</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="GenAI_UnExpInv_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))" Name="ocrDictionary" />
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="x:String" Name="promptText" />
      <Variable x:TypeArguments="x:String" Name="promptRequest" />
      <Variable x:TypeArguments="x:String" Name="customText" />
      <Variable x:TypeArguments="x:Boolean" Name="customExists" />
      <Variable x:TypeArguments="njl:JArray" Name="jArr2" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="lines" />
      <Variable x:TypeArguments="x:String" Name="strKey" />
      <Variable x:TypeArguments="x:String" Name="IonBody" />
      <Variable x:TypeArguments="njl:JToken" Name="IonBodyJson" />
      <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="out2" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JArray" Name="jArr1" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="njl:JArray" Name="dn" />
      <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="x:String" Name="outputStructure" />
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="x:String" Name="base64string" />
    </Sequence.Variables>
    <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_File_Read_2" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
    <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_Path_Validate_3" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[customExists]" Path="[customPrompt]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[customExists]" sap2010:WorkflowViewState.IdRef="If_6">
      <If.Then>
        <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_File_Read_4" sap2010:WorkflowViewState.IdRef="File_Read_2" Source="[customPrompt]" Text="[customText]" />
      </If.Then>
    </If>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[ocrDictionary]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[New Dictionary (Of String, List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[DictOcrValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <iad:CommentOut DisplayName="GenAI_UnExpInv_CommentOut_5" sap2010:WorkflowViewState.IdRef="CommentOut_3">
      <iad:CommentOut.Activities>
        <Sequence DisplayName="GenAI_UnExpInv_Sequence_6" sap2010:WorkflowViewState.IdRef="Sequence_19">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{cust_notes}",customText.tostring)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_Template_Apply_7" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':4096,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>prompt</x:String>
                  <x:String>model</x:String>
                  <x:String>version</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>promptRequest</x:String>
                  <x:String>GenAIModel</x:String>
                  <x:String>GenAIModelVersion</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[ocrOutput]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[values.toString.Replace("'","")]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </iad:CommentOut.Activities>
    </iad:CommentOut>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
      <Sequence.Variables>
        <Variable x:TypeArguments="x:String" Name="screenshotPath" />
      </Sequence.Variables>
      <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="GenAI_UnExpInv_OpenBrowser_8" sap2010:WorkflowViewState.IdRef="OpenBrowser_1" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
      <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_NavigateTo_9" sap2010:WorkflowViewState.IdRef="NavigateTo_1" URL="[Path.GetDirectoryName(documentPath) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(documentPath))]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
      <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_MaximizeWindow_10" sap2010:WorkflowViewState.IdRef="MaximizeWindow_1" WaitAfter="0" WaitBefore="0" />
      <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_SendKeys_11" sap2010:WorkflowViewState.IdRef="SendKeys_1" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
      <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_SendKeys_12" sap2010:WorkflowViewState.IdRef="SendKeys_2" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
      <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_SendKeys_13" sap2010:WorkflowViewState.IdRef="SendKeys_3" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
      <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_ScreenShot_14" sap2010:WorkflowViewState.IdRef="ScreenShot_1" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
      <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="GenAI_UnExpInv_FileToBase64_15" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_1" />
      <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_CloseBrowser_16" sap2010:WorkflowViewState.IdRef="CloseBrowser_1" />
    </Sequence>
    <Sequence DisplayName="GenAI_UnExpInv_Sequence_17" sap2010:WorkflowViewState.IdRef="Sequence_20">
      <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{notes}",customText.tostring)]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[Regex.Replace(values.tostring, "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
        </Assign.Value>
      </Assign>
      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_Template_Apply_18" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
        <ias:Template_Apply.Values>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="8">
              <x:String>prompt</x:String>
              <x:String>base64string</x:String>
              <x:String>model</x:String>
              <x:String>version</x:String>
              <x:String>ocrText</x:String>
              <x:String>outputStructure</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="8">
              <x:String>promptRequest</x:String>
              <x:String>base64string</x:String>
              <x:String>GenAIModel</x:String>
              <x:String>GenAIModelVersion</x:String>
              <x:String>value</x:String>
              <x:String>outputStructure</x:String>
            </scg:List>
          </scg:List>
        </ias:Template_Apply.Values>
      </ias:Template_Apply>
    </Sequence>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_DeserializeJSON_19" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="GenAI_UnExpInv_IONAPIRequestWizard_20" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>x-infor-logicalidprefix</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>lid://infor.syteline</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="0" />
          <scg:List x:TypeArguments="x:String" Capacity="0" />
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <Switch x:TypeArguments="x:Boolean" Expression="[genAIRespCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_1">
      <Sequence x:Key="True" DisplayName="GenAI_UnExpInv_Sequence_21" sap2010:WorkflowViewState.IdRef="Sequence_10">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
          <Assign.To>
            <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_UnExpInv_DeserializeJSON_22" sap2010:WorkflowViewState.IdRef="DeserializeJSON_3" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
        <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
          <Assign.To>
            <OutArgument x:TypeArguments="njl:JArray">[jArr1]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Details")(0).ToString)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">0</InArgument>
          </Assign.Value>
        </Assign>
        <ForEach x:TypeArguments="njl:JToken" DisplayName="GenAI_UnExpInv_ForEach_23" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[jArr1]">
          <ActivityAction x:TypeArguments="njl:JToken">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
            </ActivityAction.Argument>
            <Sequence DisplayName="GenAI_UnExpInv_Sequence_24" sap2010:WorkflowViewState.IdRef="Sequence_18">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[New String() { "TRANSACTION_DATE","VENDOR_SUPPLIER_ID","VENDOR_NAME","PAYMENT_AMOUNT","EXPENSE_CATEGORY","EXPENSE_PERIOD","ACCOUNT_CODE","COST_CENTER","DEPARTMENT_CODE","MACHINE_BUSINESS_LINE","REQUESTER_NAME","APPROVER_NAME","PAYMENT_TERM_PERIOD","VENDOR_ADDRESS","BILL_TO_NAME","BILL_TO_ADDRESS","INVOICE_NUMBER" }(i)]</InArgument>
                </Assign.Value>
              </Assign>
              <If Condition="[DictOcrValues(strKey).ToString.ToLower = &quot;n/a&quot; OR DictOcrValues(strKey).ToString.ToLower = &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_7">
                <If.Then>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Then>
                <If.Else>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Object">[item.ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Else>
              </If>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </ActivityAction>
        </ForEach>
      </Sequence>
    </Switch>
    <sads:DebugSymbol.Symbol>d1pDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXEdlbkFJX1VuRXhwSW52LnhhbWx9TQOOAw4CAQFpBWm3AQMBrQFqBWrBAQMBqAFrBXQOAwGkAXUFeQoDAZwBegWBAQ4DAZgBggEFiQEOAwGUAYoBBZEBDgMBjwGSAQWZAQ4DAYoBmgEFzwEWAwGJAdABBd0BEAIBZt4BBZ0CEAIBSp4CBZ4C1wECAUWfAgWwAh8CAT2xAgWMAw4CAQJppgFptAEDAbABaZIBaaABAwGuAWqYAWqoAQMBqwFqrgFqvgEDAakBcQtxOgMBpwFtMW09AwGlAXUTdSMDAZ0Bdwl3vQEDAZ8Bf1R/gQEDAZsBfFV8ZAMBmQGHAUqHAW0DAZcBhAFLhAFaAwGVAY8BMI8BUwMBkgGMATGMAT0DAZABlwEwlwFaAwGNAZQBMZQBRAMBiwHUAQfUAfMBAwGGAdUBB9UBswIDAYEB1gEH1gG3AQIBftcBB9cBvgECAXrYAQfYAcEBAgF22QEH2QHBAQIBctoBB9oB0wECAW3bAQfbAdABAgFo3AEH3AGUAQIBZ98BB+YBEAIBYOcBB+4BEAIBW+8BB/YBEAIBV/cBB/4BEAIBU/8BB4YCEAIBT4cCB5wCHAIBS54CyQGeAtQBAgFIngKmAZ4CuwECAUafAvUBnwKTAgIBRJ8CvAKfAs0CAgFCnwKdAp8CrgICAUCfAtICnwKKAwIBPrECNLECSwIBA7ICB4sDEgIBBXesAXe6AQMBogF3lgF3pgEDAaAB1AHDAdQB3AEDAYgB1AHsAdQB8AEDAYcB1QGQAdUB/gEDAYUB1QGrAtUBsAIDAYQB1QGJAtUBjAIDAYMB1QGYAtUBmwIDAYIB1gGiAdYBpQEDAYAB1gGxAdYBtAECAX/XAakB1wGsAQIBfdcBmgHXAZ4BAgF81wG4AdcBuwECAXvYAakB2AGsAQIBedgBmgHYAZ4BAgF42AG4AdgBvgECAXfZAakB2QGsAQIBddkBmgHZAZ4BAgF02QG4AdkBvgECAXPaAboB2gG9AQIBcdoByQHaAdABAgFw2gGdAdoBrwECAW7bATXbAUUCAWvbAYoB2wGcAQIBaeQBMuQBaAIBY+EBM+EBQgIBYewBMuwBUwIBXukBM+kBQgIBXPQBMvQBewIBWvEBM/EBRAIBWPwBMvwBewIBVvkBM/kBQgIBVIQCMoQCrQECAVKBAjOBAjoCAVCHApoQhwKlEAIBTYcCrAGHApQQAgFMswIJugISAgE5uwIJwgISAgE1wwIJygISAgEwywIJywLUAQIBK8wCCdMCEgIBJ9QCCdsCEgIBI9wCCYoDEwIBBrgCNrgCXgIBPLUCN7UCPQIBOsACNMACXwIBOL0CNb0COwIBNsgCNMgCbwIBM8UCNcUCOwIBMcsCwAHLAtEBAgEuywKqAcsCsgECASzRAjbRAmECASrOAjfOAj4CASjZAjPZAjQCASbWAjTWAjcCASTcAogB3AKRAQIBIuECDYgDGAIBB+ICD+kCGAIBHeoCD/8CFAIBDYADD4cDGAIBCOcCOucC+wICASDkAjvkAkMCAR7qAh3qApQBAgEO7AIT8wIcAgEY9gIT/QIcAgEThQM5hQM+AgELggM6ggM9AgEJ8QI+8QJCAgEc7gI/7gJWAgEZ+wI++wJNAgEX+AI/+AJWAgEU</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="627,22" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="627,22" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="627,60" />
      <sap2010:ViewStateData Id="File_Read_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="627,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="627,60" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="627,60" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="627,60" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="627,60" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,546">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="627,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="OpenBrowser_1" sap:VirtualizedContainerService.HintSize="605,58" />
      <sap2010:ViewStateData Id="NavigateTo_1" sap:VirtualizedContainerService.HintSize="605,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_1" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="SendKeys_1" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="SendKeys_2" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="SendKeys_3" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="ScreenShot_1" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="FileToBase64_1" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="CloseBrowser_1" sap:VirtualizedContainerService.HintSize="605,22" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="627,714">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="627,646">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="627,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="627,22" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="561,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="561,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="561,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_3" sap:VirtualizedContainerService.HintSize="561,22" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="561,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="561,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="509,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="531,532">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="561,680" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="583,1366">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_1" sap:VirtualizedContainerService.HintSize="627,1550" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="649,4051">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="689,4131" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
