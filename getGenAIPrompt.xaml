<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="GenAIPromptFileURL" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="GenAIStatus" Type="OutArgument(x:String)" />
    <x:Property Name="fileName" Type="InArgument(x:String)" />
    <x:Property Name="GenAIModelVersion" Type="OutArgument(x:String)" />
    <x:Property Name="GenAIModel" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>CrowdStrike.Sensor.ScriptControl</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Security.Policy</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>CrowdStrike.Sensor.ScriptControl</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="getGenAIPrompt_TryCatch_1" sap2010:WorkflowViewState.IdRef="TryCatch_7">
    <TryCatch.Variables>
      <Variable x:TypeArguments="njl:JToken" Name="model" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="getGenAIPrompt_Sequence_2" sap2010:WorkflowViewState.IdRef="Sequence_32">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:Int32" Name="rescode" />
          <Variable x:TypeArguments="iru:ResponseObject" Name="GenAIresponse" />
          <Variable x:TypeArguments="njl:JToken" Name="GenAIrespToken" />
          <Variable x:TypeArguments="njl:JToken" Name="GenAIPromptUrl" />
          <Variable x:TypeArguments="x:String" Name="url" />
          <Variable x:TypeArguments="x:Int32" Name="GenAIresponseCode" />
        </Sequence.Variables>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="getGenAIPrompt_IONAPIRequestWizard_3" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[GenAIresponse]" StatusCode="[GenAIresponseCode]" Url="[GenAIPromptFileURL]">
          <iai:IONAPIRequestWizard.Headers>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>Accept</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>application/json</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.Headers>
        </iai:IONAPIRequestWizard>
        <If Condition="[GenAIresponseCode=200]" DisplayName="getGenAIPrompt_If_4" sap2010:WorkflowViewState.IdRef="If_1">
          <If.Then>
            <Sequence DisplayName="getGenAIPrompt_Sequence_5" sap2010:WorkflowViewState.IdRef="Sequence_36">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:String" Name="variable2" />
                <Variable x:TypeArguments="njl:JToken" Name="version" />
              </Sequence.Variables>
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIStatus_6" sap2010:WorkflowViewState.IdRef="Assign_217">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">success</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIrespToken_7" sap2010:WorkflowViewState.IdRef="Assign_218">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[GenAIrespToken]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[GenAIresponse.readasjson]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:JQTransform ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getGenAIPrompt_JQTransform_8" sap2010:WorkflowViewState.IdRef="JQTransform_72" JQ="[&quot;.key.item.attrs.attr[]| select(.name == &quot;&quot;model&quot;&quot; or .name == &quot;&quot;Model&quot;&quot;) | .value&quot;]" JSON="[model]" Raw="False">
                <ias:JQTransform.Text>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>key</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>GenAIrespToken</x:String>
                    </scg:List>
                  </scg:List>
                </ias:JQTransform.Text>
              </ias:JQTransform>
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIModel_9" sap2010:WorkflowViewState.IdRef="Assign_219">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[model.tostring]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIModel_10" sap2010:WorkflowViewState.IdRef="Assign_220">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[GenAIModel.trim().substring(1,GenAIModel.length()-2)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:JQTransform ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getGenAIPrompt_JQTransform_11" sap2010:WorkflowViewState.IdRef="JQTransform_73" JQ="[&quot;.key.item.attrs.attr[]| select(.name == &quot;&quot;version&quot;&quot; or .name == &quot;&quot;Version&quot;&quot;) | .value&quot;]" JSON="[version]" Raw="False">
                <ias:JQTransform.Text>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>key</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>GenAIrespToken</x:String>
                    </scg:List>
                  </scg:List>
                </ias:JQTransform.Text>
              </ias:JQTransform>
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIModelVersion_12" sap2010:WorkflowViewState.IdRef="Assign_221">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[version.tostring]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIModelVersion_13" sap2010:WorkflowViewState.IdRef="Assign_222">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[GenAIModelVersion.trim().substring(1,GenAIModelVersion.length()-2)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:JQTransform ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getGenAIPrompt_JQTransform_14" sap2010:WorkflowViewState.IdRef="JQTransform_74" JQ=".key|.item|.resrs|.res[0]|.url" JSON="[GenAIPromptUrl]" Raw="False">
                <ias:JQTransform.Text>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>key</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>GenAIrespToken</x:String>
                    </scg:List>
                  </scg:List>
                </ias:JQTransform.Text>
              </ias:JQTransform>
              <Assign DisplayName="getGenAIPrompt_Assign_url_15" sap2010:WorkflowViewState.IdRef="Assign_223">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[GenAIPromptUrl.tostring]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign DisplayName="getGenAIPrompt_Assign_url_16" sap2010:WorkflowViewState.IdRef="Assign_224">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[url]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[url.trim().substring(1,url.length()-2)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:DownloadFile_URL Async="False" ContinueOnError="True" DisplayName="getGenAIPrompt_DownloadFile_URL_17" ErrorCode="[rescode]" sap2010:WorkflowViewState.IdRef="DownloadFile_URL_5" Name="[fileName + &quot;.txt&quot;]" OutputFile="[fileName]" Target="[configurationFolder]" URL="[url]" />
            </Sequence>
          </If.Then>
          <If.Else>
            <Sequence DisplayName="getGenAIPrompt_Sequence_18" sap2010:WorkflowViewState.IdRef="Sequence_35">
              <Assign DisplayName="getGenAIPrompt_Assign_GenAIStatus_19" sap2010:WorkflowViewState.IdRef="Assign_216">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">failure</InArgument>
                </Assign.Value>
              </Assign>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getGenAIPrompt_Append_Line_20" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;Exception raised while downloading Gen AI Prompt file:&quot;+GenAIresponse.readAsText]" Source="[logFile]" />
            </Sequence>
          </If.Else>
        </If>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getGenAIPrompt_Append_Line_21" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Exception occurred while downloading Gen AI Prompt file: &quot;+fileName + &quot;.txt with exception type &quot;+exception.GetType().Name+&quot;.&quot;]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <sads:DebugSymbol.Symbol>d1pDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXGdldEdlbkFJUHJvbXB0LnhhbWw7UwP2AQ4CAQFYB+kBEgIBCPEBC/EBzgICAQJhCWwjAgFUbQnoAQ4CAQnxAZcB8QG4AgIBBfEBwALxAcsCAgEDYakCYboCAgFZYeACYfYCAgFXYcYCYdsCAgFVbRdtMAIBCm8N2QEYAgEV3AEN5gEYAgEMdA97GAIBUHwPgwEYAgFMhAEPjwEhAgFIkAEPlwEYAgFEmAEPnwEYAgE+oAEPqwEhAgE6rAEPswEYAgE2tAEPuwEYAgEwvAEPxwEhAgEsyAEPzwEYAgEo0AEP1wEYAgEi2AEP2AGiAgIBFt0BD+QBGAIBEeUBD+UBkAICAQ15OnlBAgFTdjt2SAIBUYEBPIEBVgIBT349fk0CAU2EAZsBhAGkAgIBS4QBqgKEAbMCAgFJlQE6lQFKAgFHkgE7kgFHAgFFnQE6nQFwAgFBmgE7mgFHAgE/oAGbAaABqAICAT2gAa4CoAG5AgIBO7EBOrEBTAIBOa4BO64BTgIBN7kBOrkBfgIBM7YBO7YBTgIBMbwBmwG8AbsBAgEvvAHBAbwB0wECAS3NATrNAVMCASvKATvKAUACASnVATrVAWICASXSATvSAUACASPYAZgC2AGfAgIBINgB6AHYAfQBAgEe2AH8AdgBkwICARzYASvYATICARvYAXfYAYIBAgEZ2AG9AdgB3AECARfiATriAUECARTfATvfAUgCARLlAZsB5QH6AQIBEOUBggLlAY0CAgEO</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_217" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_218" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="JQTransform_72" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_219" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_220" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="JQTransform_73" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_221" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_222" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="JQTransform_74" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_223" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="DownloadFile_URL_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="264,1148">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_216" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="554,1302">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="576,1488">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="580.666666666667,105.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="594.666666666667,1913.33333333333" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="634.666666666667,1993.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldExpandAll">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
