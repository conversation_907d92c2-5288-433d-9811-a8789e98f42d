﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_DEV/" this:Workflow.businessRule="AP_RespPerson" this:Workflow.VendorID="182881" this:Workflow.division="670" this:Workflow.company="999"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="businessRule" Type="InArgument(x:String)" />
    <x:Property Name="VendorID" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="APResp" Type="OutArgument(x:String)" />
    <x:Property Name="User_GUID" Type="OutArgument(x:String)" />
    <x:Property Name="Buyer" Type="InArgument(x:String)" />
    <x:Property Name="APTeam" Type="InArgument(x:String)" />
    <x:Property Name="poNumber" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="APResp2_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="x:String" Name="businessRulesAPIURL" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="Req" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="Response" />
      <Variable x:TypeArguments="x:Int32" Name="ResponseCode" />
      <Variable x:TypeArguments="x:String" Name="erpAccountingEntity" />
      <Variable x:TypeArguments="x:String" Name="BuyerName" />
      <Variable x:TypeArguments="x:String" Name="ApTeamName" />
    </Sequence.Variables>
    <Assign DisplayName="APResp2_Assign_APResp_2" sap2010:WorkflowViewState.IdRef="Assign_9">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="APResp2_Assign_User_GUID_3" sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[""]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="APResp2_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;CustomerApi/APRAPI/Authorization&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>CONO</x:String>
              <x:String>SUNO</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>company</x:String>
              <x:String>VendorID</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[StatusCode1 = 200]" sap2010:WorkflowViewState.IdRef="If_3">
        <If.Then>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[respObj1.ReadAsJson]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[out1("AP Responsible").ToString]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </If.Then>
      </If>
    </Sequence>
    <If Condition="[APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="APResp2_IONAPIRequestWizard_5" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;CustomerApi/APRAPI/APAuthorization/CUSEXTMI/GetFieldValue&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>cono</x:String>
                  <x:String>PK01</x:String>
                  <x:String>PK02</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>company</x:String>
                  <x:String>VendorID</x:String>
                  <x:String>division</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode1 = 200]" sap2010:WorkflowViewState.IdRef="If_4">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[respObj1.ReadAsJson]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("AP Responsible").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
      <If.Then>
        <Sequence DisplayName="APResp2_Sequence_6" sap2010:WorkflowViewState.IdRef="Sequence_4">
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="APResp2_Template_Apply_7" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'parameters': { 'Supplier': '{{%VendorID%}}','Division':'{{%division%}}','Company':'{{%company%}}' } }" Text="[brFomat]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VendorID</x:String>
                  <x:String>division</x:String>
                  <x:String>company</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VendorID</x:String>
                  <x:String>division</x:String>
                  <x:String>company</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[businessRulesAPIURL]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[TenantID +"IONSERVICES/businessrules/decision/execute/"+businessRule]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="APResp2_DeserializeJSON_8" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="APResp2_IONAPIRequestWizard_9" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID +&quot;IONSERVICES/businessrules/decision/execute/&quot;+businessRule]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VendorID</x:String>
                  <x:String>Division</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>vendorID</x:String>
                  <x:String>division</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <TryCatch DisplayName="APResp2_TryCatch_10" sap2010:WorkflowViewState.IdRef="TryCatch_1">
            <TryCatch.Try>
              <If Condition="[respOutput.ReadAsJson(&quot;parameters&quot;).HasValues]" DisplayName="APResp2_If_11" sap2010:WorkflowViewState.IdRef="If_1">
                <If.Then>
                  <Assign DisplayName="APResp2_Assign_APResp_12" sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("APperson").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Then>
                <If.Else>
                  <Assign DisplayName="APResp2_Assign_APResp_13" sap2010:WorkflowViewState.IdRef="Assign_7">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Else>
              </If>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <Assign DisplayName="APResp2_Assign_APResp_14" sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">UNKNOWN</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <If Condition="[Buyer.Contains(&quot;True&quot;)]" DisplayName="APResp2_If_15" sap2010:WorkflowViewState.IdRef="If_7">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                <Assign DisplayName="APResp2_Assign_Req_16" sap2010:WorkflowViewState.IdRef="Assign_39">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Req]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["PUNO : "+poNumber+" AND DIVI : " +division]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="APResp2_IONAPIRequestWizard_17" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[Response]" StatusCode="[ResponseCode]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/SearchHead?&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>SQRY</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Req</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <Assign DisplayName="APResp2_Assign_BuyerName_18" sap2010:WorkflowViewState.IdRef="Assign_34">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[BuyerName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Response.readasjson("results")(0)("records")(0)("BUYE").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Template_Apply ContinueOnError="True" DisplayName="APResp2_Template_Apply_19" ErrorCode="[brRespStatus]" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{ 'erpPersonIds': ['{{%erpPersonIds%}}'], 'includeUserProperties': true}" Text="[brFomat]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>erpPersonIds</x:String>
                        <x:String>erpAccountingEntity</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>BuyerName</x:String>
                        <x:String>erpAccountingEntity</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="APResp2_DeserializeJSON_20" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" sap2010:Annotation.AnnotationText="API call of erppersonids to get User_GUID value" ContentType="application_json" ContinueOnError="True" DisplayName="APResp2_IONAPIRequestWizard_21" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" PostData="[brDesOpt]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID+&quot;ifsservice/usermgt/v2/users/search/erppersonids&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[brRespStatus = 200]" sap2010:WorkflowViewState.IdRef="If_15">
                  <If.Then>
                    <If Condition="[JToken.Parse(respOutput.ReadAsText)(&quot;response&quot;)(&quot;userlist&quot;)(0)(&quot;id&quot;).tostring &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_14">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
                          <Assign DisplayName="APResp2_Assign_User_GUID_22" sap2010:WorkflowViewState.IdRef="Assign_28">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("response")("userlist")(0)("id").tostring]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Then>
                    </If>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <If Condition="[APTeam.Contains(&quot;True&quot;)]" DisplayName="APResp2_If_23" sap2010:WorkflowViewState.IdRef="If_21">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:Int32" Name="ResponseCode" />
                </Sequence.Variables>
                <ias:Template_Apply ContinueOnError="True" DisplayName="APResp2_Template_Apply_24" ErrorCode="[brRespStatus]" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ 'erpPersonIds': ['{{%erpPersonIds%}}'], 'includeUserProperties': true}" Text="[brFomat]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>erpPersonIds</x:String>
                        <x:String>erpAccountingEntity</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>APResp</x:String>
                        <x:String>erpAccountingEntity</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="APResp2_DeserializeJSON_25" sap2010:WorkflowViewState.IdRef="DeserializeJSON_4" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" sap2010:Annotation.AnnotationText="API call of erppersonids to get User_GUID value" ContentType="application_json" ContinueOnError="True" DisplayName="APResp2_IONAPIRequestWizard_26" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" PostData="[brDesOpt]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID+&quot;ifsservice/usermgt/v2/users/search/erppersonids&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[brRespStatus = 200]" sap2010:WorkflowViewState.IdRef="If_20">
                  <If.Then>
                    <If Condition="[JToken.Parse(respOutput.ReadAsText)(&quot;response&quot;)(&quot;userlist&quot;)(0)(&quot;id&quot;).tostring &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_19">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                          <If Condition="[User_GUID = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_18">
                            <If.Then>
                              <Assign DisplayName="APResp2_Assign_User_GUID_27" sap2010:WorkflowViewState.IdRef="Assign_37">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("response")("userlist")(0)("id").tostring]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </If.Then>
                            <If.Else>
                              <Assign DisplayName="APResp2_Assign_User_GUID_28" sap2010:WorkflowViewState.IdRef="Assign_38">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(User_GUID+","+JToken.Parse(respOutput.ReadAsText)("response")("userlist")(0)("id").tostring).Trim]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </If.Else>
                          </If>
                        </Sequence>
                      </If.Then>
                    </If>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1FDOlxVc2Vyc1xybmFnZW5kcmExXEFwcERhdGFcTG9jYWxcSW5mb3JSUEFcTTNJbnZvaWNlUHJvY2Vzc2luZ0dlbkFJVjNcQVBSZXNwLnhhbWyKAQGLAgGPAgEGAacCAasCAQUBxAEB0gEBBAHrAQHyAQEDAW4BpwEBAk0D+AMOAgEBXgVnDgMBvgFoBW8OAwG6AXAFoQEQAwGmAaIBBdcBCgMBjwHYAQW9AgoCAWS+AgX2AwoCAQJkC2Q6AwHBAWAxYDkDAb8BbTBtNAMBvQFqMWo8AwG7AXEHigEhAwGzAYsBB6ABDAMBpwGiAROiASwDAZABpAEJ1QEUAwGSAdgBE9gBLAIBZdoBCbsCFAIBZ74CE74CLAIBA8ACCccCEgIBYMoCCfQDFAIBBXGTAnGfAgMBuAFxvwJx+gIDAbYBcasCcboCAwG0AYsBFYsBKgMBqAGNAQueARYDAaoBpQELvgElAwGfAb8BC9QBEAMBkwHbAQvqASADAYsB6wEL8gEUAwGFAfMBC/MB1AEDAYAB9AELiwIlAgF3jAILugIWAgFoxQI0xQI7AgFjwgI1wgI9AgFhywILqQMQAgEuqgML8wMQAgEGjgENlQEWAwGvAZYBDZ0BFgMBqwGlAZcCpQGjAgMBpAGlAcMCpQGXAwMBogGlAa8CpQG+AgMBoAG/ARm/AS4DAZQBwQEP0gEaAwGWAdsBmwLbAaYCAwGNAdsBqQHbAZUCAwGMAfABNvABfAMBiAHtATftAUwDAYYB8wHGAfMB0QEDAYMB8wGsAfMBuAEDAYEB9AH9AfQBlAICAX/0AZ4C9AGsAgIBffQBzQL0AZ8DAgF69AG4AvQByAICAXiOAg+lAhQCAW2tAhO2AhwCAWnLAhnLAj0CAS/NAg+nAxoCATGqAxmqAz4CAQesAw/xAxoCAQmTATqTAU8DAbIBkAE7kAFBAwGwAZsBOJsBWQMBrgGYATmYAUEDAawBwgERyQEaAwGbAcoBEdEBGgMBlwGOAh2OAlgCAW6QAhOXAhwCAXOaAhOjAhwCAW+zAhmzAkgCAWyvAj+vAkcCAWrOAhHVAhoCAVrWAhHrAisCAVPsAhHzAhoCAU/0AhGBAyYCAUmCAxGCA9oBAgFEgwMRlAMrAgE8lQMRpgMWAgEysAMRvQMmAgEovgMRvgPaAQIBI78DEdADKwIBG9EDEfADFgIBCscBPscBUwMBngHEAT/EAUUDAZwBzwE8zwFdAwGaAcwBPcwBRQMBmAGVAj6VAngCAXaSAj+SAkcCAXSgAhmgAkgCAXKcAj+cAkcCAXDTAjzTAmkCAV3QAj3QAkICAVvWAp8C1gKrAgIBWNYCzALWApQDAgFW1gK3AtYCxwICAVTxAjzxAn4CAVLuAj3uAkgCAVD0AocC9AKSAgIBTfQCtQH0AoECAgFM9AJo9AJ4AgFKggPMAYID1wECAUeCA7IBggO+AQIBRYMD1wKDA+MCAgFDgwPtAoMD+wICAUGDA5wDgwPkAwIBP4MDhwODA5cDAgE9lQMflQM1AgEzlwMVpAMaAgE1sAOHArADkgICASywA7UBsAOBAgIBK7ADaLADeAIBKb4DzAG+A9cBAgEmvgOyAb4DvgECASS/A9cCvwPjAgIBIr8D7QK/A/sCAgEgvwOcA78D5AMCAR6/A4cDvwOXAwIBHNEDH9EDNQIBC9MDFe4DGgIBDZcDI5cDqAECATaZAxmiAyQCATfTAyPTA6gBAgEO1QMZ7AMkAgEPmgMboQMkAgE41gMb6wMgAgEQnwNGnwOVAQIBO5wDR5wDUgIBOdYDKdYDRQIBEdgDH98DKAIBF+IDH+kDKAIBE90DSt0DmQECARraA0vaA1YCARjnA0rnA64BAgEW5ANL5ANWAgEU</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1217,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,432" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="1217,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="464,432" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="1217,766" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="528.8,22.4" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="528.8,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="528.8,22.4" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="528.8,22.4" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="243.2,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="243.2,60" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="511.2,209.6">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="243.2,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="514.4,130.4">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="528.8,652.8" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="1217,208" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="589,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="589,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="611,1052">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="928,1200">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="781,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_4" sap:VirtualizedContainerService.HintSize="781,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="781,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="531,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="656,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="781,628">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="803,938">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="928,1086">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="950,2450">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="1217,2598" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="1239,4634">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1279,4914">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldExpandAll">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
