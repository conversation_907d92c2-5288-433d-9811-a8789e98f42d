﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.businessRule="ToleranceCheck" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:this="clr-namespace:RehostedWorkflowDesigner" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="VendorID" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="amount" Type="OutArgument(x:String)" />
    <x:Property Name="percentage" Type="OutArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="businessRule" Type="InArgument(x:String)" />
    <x:Property Name="QtyPercentage" Type="OutArgument(x:String)" />
    <x:Property Name="QtyAmount" Type="OutArgument(x:String)" />
    <x:Property Name="Currency" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="DivisionVendorTolerance_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="x:String" Name="matrixName" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="x:String" Name="businessRulesAPIURL" />
    </Sequence.Variables>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
      <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[businessRule]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">ToleranceCheck</InArgument>
        </Assign.Value>
      </Assign>
      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="DivisionVendorTolerance_Template_Apply_2" sap2010:WorkflowViewState.IdRef="Template_Apply_3" Template="{}{ 'parameters': { 'VendorID':'{{%VendorID%}}','Division':'{{%division%}}','Currency':'{{%Currency%}}' } }" Text="[brFomat]">
        <ias:Template_Apply.Values>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>VendorID</x:String>
              <x:String>division</x:String>
              <x:String>Currency</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>VendorID</x:String>
              <x:String>division</x:String>
              <x:String>Currency</x:String>
            </scg:List>
          </scg:List>
        </ias:Template_Apply.Values>
      </ias:Template_Apply>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[businessRulesAPIURL]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[TenantID +"IONSERVICES/businessrules/decision/execute/"+businessRule]</InArgument>
        </Assign.Value>
      </Assign>
      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="DivisionVendorTolerance_DeserializeJSON_3" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="DivisionVendorTolerance_IONAPIRequestWizard_4" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID +&quot;IONSERVICES/businessrules/decision/execute/&quot;+businessRule]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>VendorID</x:String>
              <x:String>Division</x:String>
              <x:String>Currency</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>vendorID</x:String>
              <x:String>division</x:String>
              <x:String>Currency</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respOutput.ReadAsJson(&quot;parameters&quot;).HasValues]" sap2010:WorkflowViewState.IdRef="If_2">
        <If.Then>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("PriceAmount").toString]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[percentage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("PricePercentage").toString]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[QtyAmount]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("QtyAmount").toString]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[QtyPercentage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("QtyPercentage").toString]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </If.Then>
        <If.Else>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[percentage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[QtyAmount]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[QtyPercentage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </If.Else>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>d2NDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXERpdmlzaW9uVmVuZG9yVG9sZXJhbmNlLnhhbWwwAXIBgQEBAmID+wEOAgEBawX5ARACAQJsB3MQAgE/dAeDARwCATuEAQeLARACATWMAQeMAdABAgEwjQEHpgEhAgEnpwEH+AEMAgEDcTJxQAIBQm4zbkECAUB0mAJ0owICAT10pQF0kgICATyJATKJAXgCATiGATOGAUgCATaMAcIBjAHNAQIBM4wBqAGMAbQBAgExjQH5AY0BkAICAS+NAZoCjQGoAgIBLY0ByQKNAZsDAgEqjQG0Ao0BxAICASinARWnAVACAQSpAQvKARYCARbNAQv2ARYCAQWqAQ2xARYCASOyAQ25ARYCAR+6AQ3BARYCARvCAQ3JARYCARfOAQ3XARYCARLYAQ3hARYCAQ7iAQ3rARYCAQrsAQ31ARYCAQavATivAXUCASasATmsAUECASS3ATi3AXkCASK0ATm0AUUCASC/ATi/AXMCAR68ATm8AUQCARzHATjHAXcCARrEATnEAUgCARjUARPUAUICARXQATnQAUECARPeARPeAUICARHaATnaAUUCAQ/oARPoAUICAQ3kATnkAUQCAQvyARPyAUICAQnuATnuAUgCAQc=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="Template_Apply_3" sap:VirtualizedContainerService.HintSize="553.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="553.333333333333,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="264,484">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,484">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="553,632" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="575.333333333333,1146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="597.333333333333,1270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="637.333333333333,1350" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
