# PowerShell script to update DisplayName attributes in XAML files
# according to the naming convention: XAMLName_ActivityName_XamlLevelCounter
# Special rule for Assign activities: XAMLName_ActivityName_VariableName_XamlLevelCounter

param(
    [string]$XamlFilePath
)

function Update-XamlDisplayNames {
    param(
        [string]$FilePath
    )

    Write-Host "Processing file: $FilePath"

    # Get the XAML file name without extension
    $xamlName = [System.IO.Path]::GetFileNameWithoutExtension($FilePath)

    # Read the file content
    $content = Get-Content -Path $FilePath -Raw

    # Counter for activities at XAML level
    $activityCounter = 1

    # Function to extract variable name from Assign activity context
    function Get-AssignVariableName {
        param([string]$assignBlock)

        # Look for OutArgument pattern in the assign block
        if ($assignBlock -match '<OutArgument[^>]*>\[([^\]]+)\]</OutArgument>') {
            $varName = $matches[1]
            # If it's a property access, get just the variable name
            if ($varName -match '^([^.(]+)') {
                return $matches[1]
            }
            return $varName
        }
        return $null
    }

    # Split content into lines for easier processing
    $lines = $content -split "`r?`n"
    $updatedLines = @()

    foreach ($line in $lines) {
        $updatedLine = $line

        # Find DisplayName attributes in this line
        if ($line -match 'DisplayName="([^"]*)"') {
            # Extract the element name from the line
            if ($line -match '<(?:\w+:)?(\w+)(?:\s|>)') {
                $activityName = $matches[1]

                # Special handling for Assign activities
                if ($activityName -eq "Assign") {
                    # Look for variable name in the surrounding context
                    $varName = $null

                    # Check if this line or nearby lines contain OutArgument pattern
                    $contextStart = [Math]::Max(0, $lines.IndexOf($line) - 5)
                    $contextEnd = [Math]::Min($lines.Count - 1, $lines.IndexOf($line) + 10)

                    for ($i = $contextStart; $i -le $contextEnd; $i++) {
                        if ($lines[$i] -match '<OutArgument[^>]*>\[([^\]]+)\]</OutArgument>') {
                            $varName = $matches[1]
                            # If it's a property access, get just the variable name
                            if ($varName -match '^([^.(]+)') {
                                $varName = $matches[1]
                            }
                            break
                        }
                    }

                    if ($varName) {
                        $newDisplayName = "${xamlName}_${activityName}_${varName}_$activityCounter"
                    } else {
                        $newDisplayName = "${xamlName}_${activityName}_$activityCounter"
                    }
                } else {
                    $newDisplayName = "${xamlName}_${activityName}_$activityCounter"
                }

                # Replace the DisplayName in the line
                $updatedLine = $line -replace 'DisplayName="[^"]*"', "DisplayName=`"$newDisplayName`""
                $activityCounter++
            }
        }

        $updatedLines += $updatedLine
    }

    $updatedContent = $updatedLines -join "`r`n"

    # Write the updated content back to the file
    Set-Content -Path $FilePath -Value $updatedContent -Encoding UTF8
    Write-Host "Successfully updated: $FilePath with $($activityCounter - 1) activities"
}

# Main execution
if ($XamlFilePath) {
    if (Test-Path $XamlFilePath) {
        Update-XamlDisplayNames -FilePath $XamlFilePath
    } else {
        Write-Error "File not found: $XamlFilePath"
    }
} else {
    # Process all XAML files in current directory
    $xamlFiles = Get-ChildItem -Path "." -Filter "*.xaml"
    foreach ($file in $xamlFiles) {
        Update-XamlDisplayNames -FilePath $file.FullName
    }
}
