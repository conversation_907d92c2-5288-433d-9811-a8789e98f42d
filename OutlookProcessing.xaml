﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:si="clr-namespace:System.IO;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="directoriesNames" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="InProgressFolder" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.IO</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch - Process Outlook Emails" sap2010:WorkflowViewState.IdRef="TryCatch_5">
    <TryCatch.Variables>
      <Variable x:TypeArguments="scg:List(x:String)" Name="inProgressFiles" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="processPoResp" />
      <Variable x:TypeArguments="x:Int32" Name="processIdCount" />
      <Variable x:TypeArguments="x:Int32" Name="statsCount" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="mainListNew" />
      <Variable x:TypeArguments="x:String" Name="strMoveFile" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="approvalLists" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Default="[New List(Of List(Of String))]" Name="finalizeStoreStat" />
      <Variable x:TypeArguments="x:Boolean" Name="blnFailureExists" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="DictFileNames" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="Process Outlook Emails Sequence" sap2010:WorkflowViewState.IdRef="Sequence_72">
        <Sequence.Variables>
          <Variable x:TypeArguments="scg:List(x:String)" Name="emailsubjects" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[InProgressFolder]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="Process Outlook Emails" sap2010:WorkflowViewState.IdRef="Sequence_67">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(iae:Mail)" Default="[new List( Of Mail)]" Name="ListAllemails" />
            <Variable x:TypeArguments="x:Int32" Name="emailResponseCode" />
          </Sequence.Variables>
          <Switch x:TypeArguments="x:String" DisplayName="Read Outlook Emails as Per invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_17">
            <iae:GetOutlookEmails Filter="{x:Null}" x:Key="OutlookClientEmail" Account="[emailAccount.Trim]" ContinueOnError="False" DisplayName="Get Outlook Emails" Emails="[ListAllemails]" ErrorCode="[emailResponseCode]" Folder="[emailFolder.Trim]" sap2010:WorkflowViewState.IdRef="GetOutlookEmails_9" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
            <x:Null x:Key="OutlookGraphEmail" />
          </Switch>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_42" Line="[Environment.newLine+&quot;Outlook emails extraction is done and the total mail count is - &quot;+ListAllemails.count.tostring+Environment.newLine+&quot;Emails Response Status code : &quot;+emailResponseCode.tostring]" Source="[logfile]" />
          <Switch x:TypeArguments="x:Boolean" DisplayName="Check total mail count" Expression="[ListAllemails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
            <Sequence x:Key="True" DisplayName="Process the emails from ListAllEmails" sap2010:WorkflowViewState.IdRef="Sequence_133">
              <Sequence DisplayName="Outlook Attachment Extraction Sequence" sap2010:WorkflowViewState.IdRef="Sequence_153">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="inProgressFolder" />
                  <Variable x:TypeArguments="x:Int32" Default="0" Name="pdfs_count" />
                  <Variable x:TypeArguments="si:FileInfo" Name="fileDetails" />
                  <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="ListDownloadedFiles" />
                  <Variable x:TypeArguments="x:Boolean" Name="attachmentsAvail" />
                  <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
                  <Variable x:TypeArguments="x:String" Name="subDownloadFolder" />
                  <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="storeStats" />
                  <Variable x:TypeArguments="x:String" Name="strException" />
                  <Variable x:TypeArguments="scg:List(x:String)" Default="[new list( of string)]" Name="ListBulkNames" />
                  <Variable x:TypeArguments="x:Int32" Name="intFailureMailCounter" />
                  <Variable x:TypeArguments="x:String" Name="strJsonString" />
                </Sequence.Variables>
                <Assign DisplayName="Assign blnFailureExists" sap2010:WorkflowViewState.IdRef="Assign_239">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[blnFailureExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Initialize strJsonString" sap2010:WorkflowViewState.IdRef="Assign_240">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strJsonString]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_241">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(of list(of string))]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_242">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">1</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_243">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[inProgressFolder]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_244">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign intFailureMailCounter" sap2010:WorkflowViewState.IdRef="Assign_245">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[intFailureMailCounter]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">1</InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="iae:Mail" sap2010:Annotation.AnnotationText="All the attachments from ListAllemails will be downloaded as part of this block of code. This block also includes additional functionality of handling duplicate file names and also changing .PDF to _copy.pdf" DisplayName="ForEach ListAllemails" sap2010:WorkflowViewState.IdRef="ForEach`1_19" Values="[ListAllemails]">
                  <ActivityAction x:TypeArguments="iae:Mail">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="iae:Mail" Name="email" />
                    </ActivityAction.Argument>
                    <TryCatch DisplayName="TryCatch - Process Each email" sap2010:WorkflowViewState.IdRef="TryCatch_8">
                      <TryCatch.Variables>
                        <Variable x:TypeArguments="x:Boolean" Name="blnDownload" />
                        <Variable x:TypeArguments="x:String" Name="MailCounter" />
                        <Variable x:TypeArguments="x:String" Name="strFailureData">
                          <Variable.Default>
                            <Literal x:TypeArguments="x:String" Value="" />
                          </Variable.Default>
                        </Variable>
                      </TryCatch.Variables>
                      <TryCatch.Try>
                        <Sequence DisplayName="For each Mail Sequence" sap2010:WorkflowViewState.IdRef="Sequence_150">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="scg:List(x:String)" Name="MasterFiles" />
                            <Variable x:TypeArguments="scg:List(x:String)" Name="attachments" />
                            <Variable x:TypeArguments="x:Boolean" Name="pdfFileAvailable" />
                            <Variable x:TypeArguments="x:Double" Name="filelength" />
                            <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListMasterNames" />
                          </Sequence.Variables>
                          <If Condition="[enableMessageBoxes]" DisplayName="Check If enableMessageBoxes" sap2010:WorkflowViewState.IdRef="If_43">
                            <If.Then>
                              <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="[&quot;Email Subject :&quot;+email.subject]" />
                            </If.Then>
                          </If>
                          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in Directory - Master Downloads folder" FileType="All" Files="[MasterFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_13" IncludeSubDir="True" />
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_246">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(x:String)">[ListMasterNames]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Master Downloads folder" sap2010:WorkflowViewState.IdRef="ForEach`1_16" Values="[MasterFiles]">
                            <ActivityAction x:TypeArguments="x:String">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                              </ActivityAction.Argument>
                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_15" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[ListMasterNames]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).Tostring]</InArgument>
                              </InvokeMethod>
                            </ActivityAction>
                          </ForEach>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_247">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Boolean">[blnDownload]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Switch x:TypeArguments="x:String" DisplayName="Downlaod Attachments based on Email invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_18">
                            <x:Null x:Key="OutlookGraphEmail" />
                            <Sequence x:Key="OutlookClientEmail" DisplayName="OutlookClientEmail Sequence" sap2010:WorkflowViewState.IdRef="Sequence_135">
                              <iae:DownloadOutlookAttachment ResponseCode="{x:Null}" ContinueOnError="False" DisplayName="Download Outlook Attachment" Email="[email]" sap2010:WorkflowViewState.IdRef="DownloadOutlookAttachment_1" OutputFilePaths="[attachments]" Path="[configurationFolder+&quot;\OutlookDownloads\DownloadedFiles_1&quot;]" />
                              <Assign DisplayName="blnDownload Assign" sap2010:WorkflowViewState.IdRef="Assign_248">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[blnDownload]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </Switch>
                          <Switch x:TypeArguments="x:Boolean" DisplayName="Check blnDownload" Expression="[blnDownload]" sap2010:WorkflowViewState.IdRef="Switch`1_25">
                            <Sequence x:Key="True" DisplayName="blnDownload True" sap2010:WorkflowViewState.IdRef="Sequence_149">
                              <Assign DisplayName="Assign pdfFileAvailable as false" sap2010:WorkflowViewState.IdRef="Assign_249">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_250">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ForEach x:TypeArguments="x:String" DisplayName="Extract ZIP files in attachments" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[attachments]">
                                <ActivityAction x:TypeArguments="x:String">
                                  <ActivityAction.Argument>
                                    <DelegateInArgument x:TypeArguments="x:String" Name="attachment" />
                                  </ActivityAction.Argument>
                                  <If Condition="[attachment.contains(&quot;.zip&quot;) or attachment.contains(&quot;.ZIP&quot;)]" DisplayName="If .ZIP file Attachment" sap2010:WorkflowViewState.IdRef="If_44">
                                    <If.Then>
                                      <Sequence DisplayName="Extract Zip" sap2010:WorkflowViewState.IdRef="Sequence_136">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="x:String" Name="extractedFolderPath" />
                                          <Variable x:TypeArguments="scg:List(x:String)" Name="extractedFiles" />
                                          <Variable x:TypeArguments="x:String" Name="extractedFilePath" />
                                        </Sequence.Variables>
                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_75" Line="[&quot;ZIP File Extracted started for attachments in email - &quot;+email.subject]" Source="[logfile]" />
                                        <ias:Directory_Extract ErrorCode="{x:Null}" Password="{x:Null}" ContinueOnError="False" DisplayName="Extract Directory" sap2010:WorkflowViewState.IdRef="Directory_Extract_1" OutputFile="[extractedFolderPath]" Source="[attachment]" Target="[configurationFolder+&quot;\OutlookDownloads\DownloadedFiles_1&quot;]" />
                                        <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete ZIP File" sap2010:WorkflowViewState.IdRef="File_Delete_1" Source="[attachment]" />
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </ActivityAction>
                              </ForEach>
                              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\DownloadedFiles_1&quot;]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[ListDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_14" IncludeSubDir="True" />
                              <ForEach x:TypeArguments="x:String" DisplayName="ForEach File in OutlookDownloads" sap2010:WorkflowViewState.IdRef="ForEach`1_18" Values="[ListDownloadedFiles]">
                                <ActivityAction x:TypeArguments="x:String">
                                  <ActivityAction.Argument>
                                    <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                                  </ActivityAction.Argument>
                                  <Sequence DisplayName="Inside For each file in OutlookDownloads" sap2010:WorkflowViewState.IdRef="Sequence_147">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_251">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_19">
                                      <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_137">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                                          <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                                        </Sequence.Variables>
                                        <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_252">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_1" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                                        <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_2" Source="[item]" />
                                        <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_253">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </Switch>
                                    <Switch x:TypeArguments="x:Boolean" DisplayName=".pdf Check If attachment.contains(&quot;.pdf&quot;)" Expression="[item.contains(&quot;.pdf&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_22">
                                      <Sequence x:Key="True" DisplayName="If .pdf extension" sap2010:WorkflowViewState.IdRef="Sequence_145">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="x:String" Name="MovedFilePath" />
                                        </Sequence.Variables>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_254">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_255">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="si:FileInfo">[fileDetails]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="si:FileInfo">[New FileInfo(item)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign DisplayName="fileLength Assign" sap2010:WorkflowViewState.IdRef="Assign_256">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Double">[filelength]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Double">[Math.Round(filedetails.Length / 1024.0 / 1024.0, 2)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[filelength&lt;=5]" DisplayName="Check file Length" sap2010:WorkflowViewState.IdRef="If_49">
                                          <If.Then>
                                            <Sequence DisplayName="ProcessEmailAttachment" sap2010:WorkflowViewState.IdRef="Sequence_140">
                                              <Sequence.Variables>
                                                <Variable x:TypeArguments="x:String" Name="variable1" />
                                              </Sequence.Variables>
                                              <Assign DisplayName="Assign pdfFileAvailable to True" sap2010:WorkflowViewState.IdRef="Assign_257">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_16" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                                              </InvokeMethod>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_258">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Int32">[pdfs_count]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Int32">[pdfs_count+1]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_259">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListMasterNames.Contains(Path.GetFileName(item))]" sap2010:WorkflowViewState.IdRef="Switch`1_20">
                                                <Sequence x:Key="True" DisplayName="File Name Already Present in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_138">
                                                  <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_45">
                                                    <If.Then>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_260">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Then>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["1_"+InvoiceFileName]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_262">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_6" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[MasterDownloads]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_InforOS_A_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                                                </Sequence>
                                                <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_139">
                                                  <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_46">
                                                    <If.Then>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_263">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Then>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_264">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["1_"+InvoiceFileName]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_265">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_7" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[MasterDownloads]" targetName="[InvoiceFileName]" />
                                                </Sequence>
                                              </Switch>
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_143">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_266">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_17" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[ListBulkNames]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[InvoiceFileName]</InArgument>
                                              </InvokeMethod>
                                              <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to BulkFiles Folder" Expression="[ListBulkNames.Contains(InvoiceFileName)]" sap2010:WorkflowViewState.IdRef="Switch`1_21">
                                                <Sequence x:Key="True" DisplayName="File Name Already Present in Bulk Files" sap2010:WorkflowViewState.IdRef="Sequence_141">
                                                  <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_47">
                                                    <If.Then>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_267">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Then>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_268">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["1_"+InvoiceFileName]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_269">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_8" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_InforOS_A_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                                                </Sequence>
                                                <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_142">
                                                  <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_48">
                                                    <If.Then>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_270">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Then>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_271">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["1_"+InvoiceFileName]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_272">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_9" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" targetName="[InvoiceFileName]" />
                                                </Sequence>
                                              </Switch>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_273">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Sequence DisplayName="Storing Stats Sequence" sap2010:WorkflowViewState.IdRef="Sequence_144">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_274">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="scg:List(x:String)">[storeStats]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[New List(of String)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_18" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[emailAccount]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="email Sender InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_19" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[email.sender]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="email Receiver InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_20" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[emailAccount]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="email ReceivedTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_21" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[email.receivedTime]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="email Subject InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_22" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[email.subject]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="FileName InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_23" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="Execution start time InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_24" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[System.DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")]</InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="Execution EndTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_25" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">
                                              <Literal x:TypeArguments="x:String" Value="" />
                                            </InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="Status InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_26" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">
                                              <Literal x:TypeArguments="x:String" Value="" />
                                            </InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="Comments InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_27" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">
                                              <Literal x:TypeArguments="x:String" Value="" />
                                            </InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="ProcessID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_28" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">
                                              <Literal x:TypeArguments="x:String" Value="" />
                                            </InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="processfilename InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_29" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">
                                              <Literal x:TypeArguments="x:String" Value="" />
                                            </InArgument>
                                          </InvokeMethod>
                                          <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_30" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                          </InvokeMethod>
                                        </Sequence>
                                      </Sequence>
                                      <ias:File_Delete ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_3" Source="[item]" />
                                    </Switch>
                                    <If Condition="[item.EndsWith(&quot;.pdf&quot;) OR item.EndsWith(&quot;.PDF&quot;) OR item.EndsWith(&quot;.txt&quot;) OR item.EndsWith(&quot;.jpg&quot;) OR item.EndsWith(&quot;.jpeg&quot;) OR item.EndsWith(&quot;.png&quot;) OR item.EndsWith(&quot;.zip&quot;) OR item.EndsWith(&quot;.ZIP&quot;)]" DisplayName="Check other file extensions" sap2010:WorkflowViewState.IdRef="If_51">
                                      <If.Else>
                                        <Sequence DisplayName="Other file extensions Sequence" sap2010:WorkflowViewState.IdRef="Sequence_146">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="x:String" Name="status" />
                                            <Variable x:TypeArguments="x:String" Name="statusComments" />
                                            <Variable x:TypeArguments="x:String" Name="fileName" />
                                            <Variable x:TypeArguments="x:String" Name="message" />
                                            <Variable x:TypeArguments="x:String" Name="emailSubject" />
                                            <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                                            <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                                            <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                                            <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                                            <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                                          </Sequence.Variables>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_275">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_276">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">Wrong Format of the document</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_277">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[Path.GetFileName(item)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_278">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_279">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_280">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_50">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_281">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_FileName',&#xA;            'value': '{{%fileName%}}',&#xA;            'label': 'File Name',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                                            <ias:Template_Apply.Values>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                  <x:String>status</x:String>
                                                  <x:String>userIdentifier</x:String>
                                                  <x:String>distributionType</x:String>
                                                  <x:String>Remarks</x:String>
                                                  <x:String>RcvdTime</x:String>
                                                  <x:String>Subject</x:String>
                                                  <x:String>msg</x:String>
                                                  <x:String>fileName</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                  <x:String>status</x:String>
                                                  <x:String>userIdentifier</x:String>
                                                  <x:String>distributionType</x:String>
                                                  <x:String>statusComments</x:String>
                                                  <x:String>emailReceivedTime</x:String>
                                                  <x:String>emailSubject</x:String>
                                                  <x:String>message</x:String>
                                                  <x:String>fileName</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </ias:Template_Apply.Values>
                                          </ias:Template_Apply>
                                          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_4" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                                            <iai:IONAPIRequestWizard.QueryParameters>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>logicalId</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>lid://infor.rpa.1</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.QueryParameters>
                                          </iai:IONAPIRequestWizard>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </ActivityAction>
                              </ForEach>
                              <Switch x:TypeArguments="x:Boolean" DisplayName="If attachments Avail False" Expression="[attachmentsAvail]" sap2010:WorkflowViewState.IdRef="Switch`1_23">
                                <Sequence x:Key="False" DisplayName="No Attachment Available" sap2010:WorkflowViewState.IdRef="Sequence_148">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="status" />
                                    <Variable x:TypeArguments="x:String" Name="statusComments" />
                                    <Variable x:TypeArguments="x:String" Name="fileName" />
                                    <Variable x:TypeArguments="x:String" Name="message" />
                                    <Variable x:TypeArguments="x:String" Name="emailSubject" />
                                    <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                                    <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                                    <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                                    <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                                    <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                                  </Sequence.Variables>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_282">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_283">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["No attachments available."]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_284">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_285">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_286">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_76" Line="No pdf files available to process" Source="[logFIle]" />
                                  <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_52">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_287">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_6" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                                    <ias:Template_Apply.Values>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>status</x:String>
                                          <x:String>userIdentifier</x:String>
                                          <x:String>distributionType</x:String>
                                          <x:String>Remarks</x:String>
                                          <x:String>RcvdTime</x:String>
                                          <x:String>Subject</x:String>
                                          <x:String>msg</x:String>
                                          <x:String>fileName</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>status</x:String>
                                          <x:String>userIdentifier</x:String>
                                          <x:String>distributionType</x:String>
                                          <x:String>statusComments</x:String>
                                          <x:String>emailReceivedTime</x:String>
                                          <x:String>emailSubject</x:String>
                                          <x:String>message</x:String>
                                          <x:String>fileName</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </ias:Template_Apply.Values>
                                  </ias:Template_Apply>
                                  <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>logicalId</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>lid://infor.rpa.1</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </Sequence>
                              </Switch>
                              <Switch x:TypeArguments="x:Boolean" DisplayName="Is any PDF file available" Expression="[pdfFileAvailable]" sap2010:WorkflowViewState.IdRef="Switch`1_24">
                                <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_77" Line="[&quot;No PDF files available for email with subject - &quot;+email.subject]" Source="[logfile]" />
                              </Switch>
                            </Sequence>
                          </Switch>
                        </Sequence>
                      </TryCatch.Try>
                      <TryCatch.Catches>
                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                          <ActivityAction x:TypeArguments="s:Exception">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                            </ActivityAction.Argument>
                            <Sequence DisplayName="Catch Block For Each Mail - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_151">
                              <Assign DisplayName="Assign strException" sap2010:WorkflowViewState.IdRef="Assign_288">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[strException]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[exception.Message]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Write Line" sap2010:WorkflowViewState.IdRef="StudioWriteLine_1" Line="[&quot;Error occurred while downloading attachments from email with subject - &quot;+email.Subject]" />
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="For each mail catch block - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_78" Line="[&quot;Exception occurred for Email below &quot;+Environment.newline+&quot;Email Subject is -&quot;+email.subject+Environment.newline+&quot;Email Received time is -&quot;+email.ReceivedTime.ToString+Environment.newline+&quot;Exception Message is - &quot;+exception.Message+&quot;.&quot;]" Source="[logfile]" />
                            </Sequence>
                          </ActivityAction>
                        </Catch>
                      </TryCatch.Catches>
                      <TryCatch.Finally>
                        <If Condition="[Not blnDownload]" DisplayName="Mark emails as Unread if Download Fails" sap2010:WorkflowViewState.IdRef="If_53">
                          <If.Then>
                            <Sequence DisplayName="Failure Data Collection" sap2010:WorkflowViewState.IdRef="Sequence_152">
                              <Assign DisplayName="Assign blnFailureExists" sap2010:WorkflowViewState.IdRef="Assign_289">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[blnFailureExists]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign MailCounter" sap2010:WorkflowViewState.IdRef="Assign_290">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[MailCounter]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Right("InforOS_A",1)+intFailureMailCounter.tostring]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign strFailureData" sap2010:WorkflowViewState.IdRef="Assign_291">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[strFailureData]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[("🗂️Email Subject: " &amp; email.Subject).PadRight(60, " "c) &amp;
("⚠️ Exception Message: " &amp; strException).PadRight(60, " "c) &amp;
("🕒 Email Received Time: " &amp; email.ReceivedTime.ToString).PadRight(60, " "c)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign strJsonString" sap2010:WorkflowViewState.IdRef="Assign_292">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[strJsonString]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strJsonString="", 
"{" &amp; 
" 'dataType': 'STRING'," &amp;
" 'name': '" &amp; MailCounter &amp; "'," &amp;
" 'value': '" &amp; strFailureData &amp; "'," &amp;
" 'label': 'Failure Details - " &amp; MailCounter &amp; "'," &amp; 
" 'readOnly': true" &amp; 
" }",
strJsonString &amp; "," &amp; 
"{" &amp; 
" 'dataType': 'STRING'," &amp;
" 'name': '" &amp; MailCounter &amp; "'," &amp;
" 'value': '" &amp; strFailureData &amp; "'," &amp;
" 'label': 'Failure Details - " &amp; MailCounter &amp; "'," &amp; 
" 'readOnly': true" &amp; 
" }")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign MailCounter NULL" sap2010:WorkflowViewState.IdRef="Assign_293">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[MailCounter]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign intFailureMailCounter" sap2010:WorkflowViewState.IdRef="Assign_294">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Int32">[intFailureMailCounter]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Int32">[intFailureMailCounter+1]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Switch x:TypeArguments="x:String" DisplayName="Mark Failure emails as Unread invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                                <iae:MarkOutlookAsRead ErrorCode="{x:Null}" x:Key="OutlookClientEmail" ContinueOnError="True" DisplayName="Mark Outlook Emails as Read/Unread" sap2010:WorkflowViewState.IdRef="MarkOutlookAsRead_1" Mail="[email]" MarkAsRead="False" />
                                <x:Null x:Key="OutlookGraphEmail" />
                              </Switch>
                            </Sequence>
                          </If.Then>
                        </If>
                      </TryCatch.Finally>
                    </TryCatch>
                  </ActivityAction>
                </ForEach>
              </Sequence>
              <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[mainListNew]">
                <ActivityAction x:TypeArguments="scg:List(x:String)">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                  </ActivityAction.Argument>
                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                    <InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                    </InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                  </InvokeMethod>
                </ActivityAction>
              </ForEach>
            </Sequence>
            <Throw x:Key="False" DisplayName="No Mail Data Throw" Exception="[New SystemException(&quot;No unread emails in the mailbox to process.&quot;)]" sap2010:WorkflowViewState.IdRef="Throw_2" />
          </Switch>
          <If Condition="[blnFailureExists]" DisplayName="Notify if any Failure emails" sap2010:WorkflowViewState.IdRef="If_40">
            <If.Then>
              <Sequence DisplayName="Failure Mails Notification Sequence" sap2010:WorkflowViewState.IdRef="Sequence_131">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="notificationString" />
                  <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                  <Variable x:TypeArguments="x:String" Name="strMessageTitle" />
                  <Variable x:TypeArguments="x:String" Name="strUserIdentifier" />
                  <Variable x:TypeArguments="x:String" Name="strDistributionType" />
                </Sequence.Variables>
                <Assign DisplayName="Assign UserIdentifier" sap2010:WorkflowViewState.IdRef="Assign_232">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strUserIdentifier]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("userIdentifier").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign strDistributionType" sap2010:WorkflowViewState.IdRef="Assign_233">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strDistributionType]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("distributionType").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_231">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strMessageTitle]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["📣 M3 Invoice Processing Failure Summary " &amp; miscValues("StartTime").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ &#xA;  'message': '{{%strMessageTitle%}}',&#xA;  'category': 'RPA', &#xA;  'parameters': [&#xA;    {{%strJsonString%}}&#xA;  ], &#xA;  'distribution': [&#xA;    {&#xA;      'identifier': '{{%userIdentifier%}}',&#xA;      'type': '{{%distributionType%}}',&#xA;      'sendMail': '{{%SendEmail%}}' &#xA;    }&#xA;  ]&#xA;}" Text="[notificationString]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>strJsonString</x:String>
                        <x:String>userIdentifier</x:String>
                        <x:String>distributionType</x:String>
                        <x:String>SendEmail</x:String>
                        <x:String>strMessageTitle</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>strJsonString</x:String>
                        <x:String>strUserIdentifier</x:String>
                        <x:String>strDistributionType</x:String>
                        <x:String>TRUE</x:String>
                        <x:String>strMessageTitle</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_3" JTokenObject="[notificationToken]" JTokenString="[notificationString]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[notificationToken.tostring]" Response="[notificationResponse]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>logicalId</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>lid://infor.rpa.1</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </Sequence>
            </If.Then>
          </If>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Process Outlook Emails Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_59" Line="Downloading attachments from Outlook Completed." Source="[logfile]" />
        </Sequence>
        <Sequence DisplayName="Split Sequence" sap2010:WorkflowViewState.IdRef="Sequence_89">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(x:String)" Name="MasterDownloadedFiles" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="SplitOutput" />
          </Sequence.Variables>
          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[MasterDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
          <If Condition="[MasterDownloadedFiles.count&gt;0]" DisplayName="Check if any files in Master Downloads" sap2010:WorkflowViewState.IdRef="If_41">
            <If.Then>
              <Sequence DisplayName=" Files exist in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_132">
                <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From{{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;promptPath&quot;,ConfigurationFolder + &quot;\Classification.txt&quot;},{&quot;promptPath2&quot;,ConfigurationFolder + &quot;\GenAISplit2New.txt&quot;},{&quot;InProgressFolder&quot;,InProgressFolder},{&quot;strClassificationExtension&quot;,ConfigurationFolder + &quot;\GenAISplit1_Ext.txt&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;strSplitExtension&quot;,ConfigurationFolder + &quot;\GenAISplit2_Ext.txt&quot;}}]" ContinueOnError="False" DisplayName="Invoke Classification_Split Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" OutputArguments="[SplitOutput]" WorkflowFile="[projectPath+&quot;\Classification_Split.xaml&quot;]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_295">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[DictFileNames]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[Ctype(SplitOutput("DictFileNames"), Dictionary(Of String, String))]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification Split Sequence Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_61" Line="Classification and Split Process Completed." Source="[logfile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification Split Sequence Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_71" Line="No files available to process in Master Downlaods. Please check email notification for any failure emails." Source="[logfile]" />
            </If.Else>
          </If>
        </Sequence>
        <Sequence DisplayName="OCR Sequence" sap2010:WorkflowViewState.IdRef="Sequence_69">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(x:String)" Name="listInProgressFiles" />
            <Variable x:TypeArguments="x:String" Name="strSpecificText" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(of string)]" Name="processIdLst" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="ListReProcess" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="ListInProgressNames" />
            <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
            <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
            <Variable x:TypeArguments="x:Boolean" Name="blnReprocessExists" />
            <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
          </Sequence.Variables>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="OCR Sequence Started  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_62" Line="Get OCR Sequence Started." Source="[logfile]" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[blnReprocessExists]" Path="[configurationFolder+ &quot;\ReProcess&quot;]" />
          <Switch x:TypeArguments="x:Boolean" DisplayName="Check if Reprocess folder exists" Expression="[blnReprocessExists]" sap2010:WorkflowViewState.IdRef="Switch`1_16">
            <Sequence x:Key="True" DisplayName="Move Reprocess Files Sequence" sap2010:WorkflowViewState.IdRef="Sequence_114">
              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files from Inprogress" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(x:String)">[ListInProgressNames]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Inprogress Folder" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[listInProgressFiles]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="itemA" />
                  </ActivityAction.Argument>
                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                    <InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="scg:List(x:String)">[ListInProgressNames]</InArgument>
                    </InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(itemA).Tostring]</InArgument>
                  </InvokeMethod>
                </ActivityAction>
              </ForEach>
              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[configurationFolder+ &quot;\ReProcess&quot;]" DisplayName="Get Reprocess Files in Directory" FileType="All" Files="[ListReProcess]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_10" IncludeSubDir="True" />
              <If Condition="[ListReProcess.Count&gt;0]" DisplayName="Check Reprocess files count and Move to Inprogress" sap2010:WorkflowViewState.IdRef="If_28">
                <If.Then>
                  <ForEach x:TypeArguments="x:String" DisplayName="Move all ReProcess Files to Inprogress" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[ListReProcess]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="ReprocessFile" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_113">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetFilename(ReprocessFile)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListInProgressNames.Contains(Path.GetFileName(ReprocessFile))]" sap2010:WorkflowViewState.IdRef="Switch`1_15">
                          <Sequence x:Key="True" DisplayName="File Name Already Present in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_111">
                            <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_29">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_3" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[ReprocessFile]" Target="[configurationFolder+ &quot;\InProgress&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                          <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_112">
                            <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_30">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_5" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[ReprocessFile]" Target="[configurationFolder+ &quot;\InProgress&quot;]" />
                          </Sequence>
                        </Switch>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_193">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                </If.Then>
              </If>
            </Sequence>
            <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_67" Line="ReProcess folder doesn't exist." Source="[logFile]" />
          </Switch>
          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files from Inprogress" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_12" IncludeSubDir="True" />
          <If Condition="[listInProgressFiles.count&gt;0]" DisplayName="Check if any files in In progress folder" sap2010:WorkflowViewState.IdRef="If_42">
            <If.Then>
              <ForEach x:TypeArguments="x:String" DisplayName="Process Each file in Inprogress with GetOCRValues XAML" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[listInProgressFiles]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                  </ActivityAction.Argument>
                  <TryCatch DisplayName="TryCatch Each file in Inprogress" sap2010:WorkflowViewState.IdRef="TryCatch_7">
                    <TryCatch.Try>
                      <Sequence DisplayName="Each file in Inprogress Sequence" sap2010:WorkflowViewState.IdRef="Sequence_71">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="CurrentOCRFile" />
                          <Variable x:TypeArguments="x:String" Name="OCRTextOut" />
                          <Variable x:TypeArguments="njl:JToken" Name="ReqOCRvalues" />
                          <Variable x:TypeArguments="x:String" Name="targetPages" />
                          <Variable x:TypeArguments="scg:List(x:String)" Name="approvalList" />
                          <Variable x:TypeArguments="x:String" Name="strTempFileName" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_52" Line="[&quot;File Name : &quot;+item2.Substring(item2.LastIndexOf(&quot;\&quot;c)+1,(item2.Length()-item2.LastIndexOf(&quot;\&quot;c))-1)]" Source="[logFile]" />
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Check if SplittingDoc is True to send Specific OCR Text" Expression="[miscValues(&quot;SplittingDoc&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="Switch`1_14">
                          <Sequence x:Key="True" DisplayName="Get OCR Text Rquired for Prompt" sap2010:WorkflowViewState.IdRef="Sequence_92">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="indexTilde" />
                              <Variable x:TypeArguments="x:Int32" Name="indexLastDashBeforeTilde" />
                              <Variable x:TypeArguments="x:String" Name="SubFileName" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
                              <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strTempFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[DictFileNames(Path.GetfileName(item2)).Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[indexTilde]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[strTempFileName.IndexOf("~"c)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[indexLastDashBeforeTilde]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[strTempFileName.LastIndexOf("{"c, indexTilde)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[strTempFileName.Substring(0, indexLastDashBeforeTilde)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[SubFileName+".txt"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_301">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[If(SubFileName.EndsWith("-.txt"), SubFileName.Replace("-.txt", ".txt"), SubFileName)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_15" IncludeSubDir="True" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ListOCRData.FirstOrDefault(Function(p) Path.GetFileName(p).Trim().ToLower() = SubFileName.Trim().ToLower())]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_8" Source="[CurrentOCRFile]" Text="[OCRTextOut]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_303">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRTextOut)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_304">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[targetPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[strTempFileName.Split("~"c)(2)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_305">
                              <Assign.To>
                                <OutArgument x:TypeArguments="s:String[]">[ArrayTargetPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="s:String[]">[targetPages.Split(","c).Select(Function(x) x.Trim()).ToArray()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_306">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[JsonConvert.SerializeObject(
    ReqOCRvalues("data").
        Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
        Select(Function(p) New With {
            Key .OCR_text = p("OCR_text").ToString(),
            Key .PageNo = p("PageNo").ToString()
        })
)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                          <Sequence x:Key="False" DisplayName="Get OCR Text Required for Prompt" sap2010:WorkflowViewState.IdRef="Sequence_108">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="indexTilde" />
                              <Variable x:TypeArguments="x:Int32" Name="indexLastDashBeforeTilde" />
                              <Variable x:TypeArguments="x:String" Name="SubFileName" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
                              <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetfileName(item2).Split("{"c)(0)+".txt"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_9" IncludeSubDir="True" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ListOCRData.FirstOrDefault(Function(p) Path.GetFileName(p).Trim().ToLower() = SubFileName.Trim().ToLower())]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_7" Source="[CurrentOCRFile]" Text="[OCRTextOut]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRTextOut)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ReqOCRvalues.ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,item2},{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;manualEntry&quot;,false},{&quot;emailSubject&quot;,&quot;NA&quot;},{&quot;emailReceivedTime&quot;,&quot;NA&quot;},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;strSpecificText&quot;,strSpecificText},{&quot;finalizeStoreStat&quot;,finalizeStoreStat}}]" ContinueOnError="False" DisplayName="GetOCRValues.xaml Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_39" OutputArguments="[processPoResp]" ResponseCode="[processPoRespCode]" WorkflowFile="[projectPath+&quot;\GetOCRValuesNew.xaml&quot;]" />
                        <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" DisplayName="Check If AutomateApproval is True" sap2010:WorkflowViewState.IdRef="If_31">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_115">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[CType(processPoResp("approvalList"), List(Of String))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[approvalList.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_32">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_116">
                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                    </InvokeMethod>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_64" Line="[&quot;Updated the approval list with the invoice number &quot; + approvalList(1).ToString]" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                      </Sequence>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <Sequence DisplayName="For each file in inprogress catch block" sap2010:WorkflowViewState.IdRef="Sequence_110">
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_53" Line="[&quot;Below Exception occured in Get OCR Values New. xaml at file - &quot; +path.getFileName(item2)+Environment.NewLine+exception.message]" Source="[logfile]" />
                            <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_1" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[item2]" Target="[configurationFolder+ &quot;\ReProcess&quot;]" />
                          </Sequence>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                </ActivityAction>
              </ForEach>
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="OCR Sequence Append Line - No files in Inprogress folder" sap2010:WorkflowViewState.IdRef="Append_Line_74" Line="No files available to process in In Progress folder." Source="[logfile]" />
            </If.Else>
          </If>
          <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot; AND approvalLists.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_34">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;approvalLists&quot;,approvalLists},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="Invoke approval Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_51" WorkflowFile="[projectPath+&quot;\approval.xaml&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_70" Line="[&quot;Below Error Occurred in Outlook_ProcessNew Xaml.&quot;+environment.newline+exception.message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="498.666666666667,62" />
      <sap2010:ViewStateData Id="GetOutlookEmails_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_17" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_42" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_239" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_240" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_241" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_242" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_243" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_244" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_245" sap:VirtualizedContainerService.HintSize="444.666666666667,62" />
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_13" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_246" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_15" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_16" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_247" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="DownloadOutlookAttachment_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_248" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,245.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_18" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_249" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_250" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_75" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Extract_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Delete_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="222,270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_14" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_251" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_252" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_253" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_137" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_19" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_254" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_255" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_256" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_257" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_16" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_258" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_259" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_260" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_138" sap:VirtualizedContainerService.HintSize="264,442.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_263" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="464,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_264" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_265" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="File_Move_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_20" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="264,656.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_266" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_17" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_267" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_268" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_269" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="264,442.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_270" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_271" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_272" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_21" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="264,452.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_273" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_274" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_18" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_19" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_20" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_21" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_22" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_23" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_24" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_25" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_26" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_27" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_28" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_29" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_30" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_144" sap:VirtualizedContainerService.HintSize="264,2448">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="286,3072.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Delete_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_22" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_275" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_276" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_277" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_278" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_279" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_280" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_281" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="264,974.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_147" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_18" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_282" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_283" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_76" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_287" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="264,934.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_23" sap:VirtualizedContainerService.HintSize="284.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_77" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_24" sap:VirtualizedContainerService.HintSize="284.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_149" sap:VirtualizedContainerService.HintSize="306.666666666667,1038">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_25" sap:VirtualizedContainerService.HintSize="476.666666666667,145.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="498.666666666667,929.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_288" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="StudioWriteLine_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_78" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_151" sap:VirtualizedContainerService.HintSize="264,310">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="400,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_289" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_290" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="MarkOutlookAsRead_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_152" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="414,552.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_19" sap:VirtualizedContainerService.HintSize="444.666666666667,763.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_153" sap:VirtualizedContainerService.HintSize="284.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="284.666666666667,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="306.666666666667,503.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Throw_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="476.666666666667,718">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_232" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_233" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_231" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="264,576">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="476.666666666667,730">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_59" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="498.666666666667,1944.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_295" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_61" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="264,310">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_71" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_89" sap:VirtualizedContainerService.HintSize="498.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_62" sap:VirtualizedContainerService.HintSize="774,22" />
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="774,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="774,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_112" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_15" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_193" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_113" sap:VirtualizedContainerService.HintSize="264,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="294.666666666667,533.333333333333" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_67" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_16" sap:VirtualizedContainerService.HintSize="774,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_12" sap:VirtualizedContainerService.HintSize="774,22" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_52" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="264,1333.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,616">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_14" sap:VirtualizedContainerService.HintSize="476.666666666667,830.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_39" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Append_Line_64" sap:VirtualizedContainerService.HintSize="217.333333333333,22" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="239.333333333333,320">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="498.666666666667,1273.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_53" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Move_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="503.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="517.333333333333,1511.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="548,1664">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_74" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="774,1818">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_51" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="774,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="498.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="520.666666666667,2356">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_70" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="525.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="539.333333333333,2714" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="579.333333333333,2914" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>