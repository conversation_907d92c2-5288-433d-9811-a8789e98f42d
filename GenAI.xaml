﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iad="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop" xmlns:iad1="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt" Type="InArgument(x:String)" />
    <x:Property Name="values" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="OutArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="ListOcrLineValues" Type="OutArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Windows.Forms</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Windows.Forms</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:Annotation.AnnotationText="Update: 5th Aug, 2025&#xA;Amount Formatting handled during extraction at Header and Line level including charges." DisplayName="GenAI_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="x:String" Name="promptText" />
      <Variable x:TypeArguments="x:String" Name="promptRequest" />
      <Variable x:TypeArguments="x:String" Name="screenshotPath" />
      <Variable x:TypeArguments="x:String" Name="base64string" />
      <Variable x:TypeArguments="x:String" Name="customText" />
      <Variable x:TypeArguments="x:Boolean" Name="customExists" />
      <Variable x:TypeArguments="x:String" Name="IonBody" />
      <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
      <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
      <Variable x:TypeArguments="njl:JToken" Name="IonBodyJson" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="out2" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JArray" Name="HeadersJArray" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="x:String" Name="strKey" />
      <Variable x:TypeArguments="x:String" Name="strDeliveryNote" />
      <Variable x:TypeArguments="x:Int32" Name="intLineDictCOunter" />
      <Variable x:TypeArguments="x:String" Name="strPONumber" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="x:String" Name="outputStructure" />
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListCommaDotKeys" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_347">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[ListCommaDotKeys]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)" xml:space="preserve">[New List(Of String) From {
    "TOTAL",
    "SUBTOTAL",
    "VAT_AMOUNT",
    "SHIPPING_AND_HANDLING_AMOUNT",
    "Invoice_Level_Discount_AMOUNT",
    "OtherCharges_AMOUNT",
    "UNIT_PRICE",
    "LINE_AMOUNT"
}]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="GenAI_Sequence_2" sap2010:WorkflowViewState.IdRef="Sequence_46">
      <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_File_Read_3" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
      <Assign DisplayName="GenAI_Assign_DictOcrValues_4" sap2010:WorkflowViewState.IdRef="Assign_113">
        <Assign.To>
          <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[DictOcrValues]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"INVOICE_RECEIPT_ID", ""},
    {"INVOICE_RECEIPT_DATE", ""},
    {"TOTAL", ""},
    {"SUBTOTAL", ""},
    {"PO_NUMBER", ""},
    {"VENDOR_NAME", ""},
    {"VENDOR_ADDRESS", ""},
    {"VENDOR_PHONE", ""},
    {"SHIP_TO_ADDRESS", ""},
    {"BILL_TO_ADDRESS", ""},
    {"ISO_CURRENCY_CODE", ""},
    {"REFERENCE", ""},
    {"UNIQUE_REGISTRATION_CODE", ""},
    {"DOCUMENT_CLASS", ""},
    {"REMIT_TO_NAME", ""},
    {"REMIT_TO_ADDRESS", ""},
    {"VAT_PERCENTAGE", ""},
    {"VAT_SUBTOTAL", ""},
    {"VAT_AMOUNT", ""},
    {"SHIPPING_AND_HANDLING_PERCENTAGE", ""},
    {"SHIPPING_AND_HANDLING_SUBTOTAL", ""},
    {"SHIPPING_AND_HANDLING_AMOUNT", ""},
    {"Invoice_Level_Discount_PERCENTAGE", ""},
    {"Invoice_Level_Discount_SUBTOTAL", ""},
    {"Invoice_Level_Discount_AMOUNT", ""},
    {"OtherCharges_PERCENTAGE", ""},
    {"OtherCharges_SUBTOTAL", ""},
    {"OtherCharges_AMOUNT", ""},
    {"DELIVERY_NOTE_DATA", ""},
    {"IBAN",""},
    {"EXCEPTION_CATEGORY",""}
}]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign DisplayName="GenAI_Assign_ListOcrLineValues_5" sap2010:WorkflowViewState.IdRef="Assign_114">
        <Assign.To>
          <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListOcrLineValues]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String,Object))]</InArgument>
        </Assign.Value>
      </Assign>
      <Sequence DisplayName="GenAI_Sequence_6" sap2010:WorkflowViewState.IdRef="Sequence_29">
        <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="GenAI_OpenBrowser_7" sap2010:WorkflowViewState.IdRef="OpenBrowser_2" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
        <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="GenAI_NavigateTo_8" sap2010:WorkflowViewState.IdRef="NavigateTo_2" URL="[Path.GetDirectoryName(documentPath) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(documentPath))]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
        <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_MaximizeWindow_9" sap2010:WorkflowViewState.IdRef="MaximizeWindow_2" WaitAfter="0" WaitBefore="0" />
        <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_SendKeys_10" sap2010:WorkflowViewState.IdRef="SendKeys_1" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
        <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_SendKeys_11" sap2010:WorkflowViewState.IdRef="SendKeys_3" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
        <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_SendKeys_12" sap2010:WorkflowViewState.IdRef="SendKeys_2" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
        <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_ScreenShot_13" sap2010:WorkflowViewState.IdRef="ScreenShot_2" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
        <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="GenAI_FileToBase64_14" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_2" />
        <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_CloseBrowser_15" sap2010:WorkflowViewState.IdRef="CloseBrowser_2" />
      </Sequence>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_Path_Validate_16" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[customExists]" Path="[customPrompt]" />
      <If Condition="[customExists]" DisplayName="GenAI_If_17" sap2010:WorkflowViewState.IdRef="If_26">
        <If.Then>
          <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_File_Read_18" sap2010:WorkflowViewState.IdRef="File_Read_5" Source="[customPrompt]" Text="[customText]" />
        </If.Then>
      </If>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_322">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_323">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
        </Assign.Value>
      </Assign>
      <iad1:CommentOut DisplayName="GenAI_CommentOut_19" sap2010:WorkflowViewState.IdRef="CommentOut_41">
        <iad1:CommentOut.Activities>
          <Sequence DisplayName="GenAI_Sequence_20" sap2010:WorkflowViewState.IdRef="Sequence_124">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{notes}",customText.tostring)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{text}",Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty))]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_Template_Apply_21" sap2010:WorkflowViewState.IdRef="Template_Apply_7" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA;'model': '{{%model%}}',   'version': '{{%version%}}','encoded_image': 'data:image/png;base64,{{%base64string%}}'}&#xA;" Text="[IonBody]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>prompt</x:String>
                    <x:String>base64string</x:String>
                    <x:String>model</x:String>
                    <x:String>version</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>promptRequest</x:String>
                    <x:String>base64string</x:String>
                    <x:String>GenAIModel</x:String>
                    <x:String>GenAIModelVersion</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
          </Sequence>
        </iad1:CommentOut.Activities>
      </iad1:CommentOut>
      <Sequence DisplayName="GenAI_Sequence_22" sap2010:WorkflowViewState.IdRef="Sequence_125">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_326">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_327">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_328">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_329">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_330">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[Regex.Replace(values.tostring, "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_Template_Apply_23" sap2010:WorkflowViewState.IdRef="Template_Apply_8" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
          <ias:Template_Apply.Values>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>prompt</x:String>
                <x:String>base64string</x:String>
                <x:String>model</x:String>
                <x:String>version</x:String>
                <x:String>ocrText</x:String>
                <x:String>outputStructure</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="8">
                <x:String>promptRequest</x:String>
                <x:String>base64string</x:String>
                <x:String>GenAIModel</x:String>
                <x:String>GenAIModelVersion</x:String>
                <x:String>value</x:String>
                <x:String>outputStructure</x:String>
              </scg:List>
            </scg:List>
          </ias:Template_Apply.Values>
        </ias:Template_Apply>
      </Sequence>
      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_DeserializeJSON_24" sap2010:WorkflowViewState.IdRef="DeserializeJSON_6" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="GenAI_IONAPIRequestWizard_25" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" StatusCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>x-infor-logicalidprefix</x:String>
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>lid://infor.colemanddp</x:String>
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="0" />
            <scg:List x:TypeArguments="x:String" Capacity="0" />
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_314">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[strDeliveryNote]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
    </Sequence>
    <Switch x:TypeArguments="x:Boolean" DisplayName="GenAI_Switch_26" Expression="[genAIRespCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_28">
      <Sequence x:Key="True" DisplayName="GenAI_Sequence_27" sap2010:WorkflowViewState.IdRef="Sequence_55">
        <Sequence.Variables>
          <Variable x:TypeArguments="njl:JArray" Name="LinesJarray" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="lines" />
          <Variable x:TypeArguments="njl:JObject" Name="jobj" />
        </Sequence.Variables>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
          <Assign.To>
            <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''").Replace("\","\\").replace("\\""","")]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="GenAI_DeserializeJSON_28" sap2010:WorkflowViewState.IdRef="DeserializeJSON_7" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
         <Sequence DisplayName="GenAI_Sequence_29" sap2010:WorkflowViewState.IdRef="Sequence_57">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="strTempAmount" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JArray">[HeadersJArray]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(Jout("Headers"))]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="njl:JToken" DisplayName="GenAI_ForEach_30" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[HeadersJArray]">
            <ActivityAction x:TypeArguments="njl:JToken">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
              </ActivityAction.Argument>
              <Sequence DisplayName="GenAI_Sequence_31" sap2010:WorkflowViewState.IdRef="Sequence_68">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_151">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[New String() {
    "INVOICE_RECEIPT_ID", "INVOICE_RECEIPT_DATE", "TOTAL", "SUBTOTAL",
    "PO_NUMBER", "VENDOR_NAME", "VENDOR_ADDRESS", "VENDOR_PHONE",
    "SHIP_TO_ADDRESS", "BILL_TO_ADDRESS", "ISO_CURRENCY_CODE", "REFERENCE",
    "UNIQUE_REGISTRATION_CODE", "DOCUMENT_CLASS", "REMIT_TO_NAME", "REMIT_TO_ADDRESS", "IBAN"
}(i)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Switch x:TypeArguments="x:Boolean" DisplayName="GenAI_Switch_32" Expression="[ListCommaDotKeys.Contains(strKey)]" sap2010:WorkflowViewState.IdRef="Switch`1_29">
                  <Sequence x:Key="True" DisplayName="GenAI_Sequence_33" sap2010:WorkflowViewState.IdRef="Sequence_131">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:Boolean" Name="hasComma" />
                      <Variable x:TypeArguments="x:Boolean" Name="hasDot" />
                      <Variable x:TypeArguments="x:String" Name="amount" />
                    </Sequence.Variables>
                    <Assign DisplayName="GenAI_Assign_DictOcrValues_34" sap2010:WorkflowViewState.IdRef="Assign_356">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Object">[item]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="GenAI_Assign_DictOcrValues_35" sap2010:WorkflowViewState.IdRef="Assign_407">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Regex.Replace(DictOcrValues(strKey).Tostring, "[^\d.,]", "").Trim()]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="GenAI_Assign_amount_36" sap2010:WorkflowViewState.IdRef="Assign_348">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[DictOcrValues(strKey).Tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="GenAI_Assign_hasComma_37" sap2010:WorkflowViewState.IdRef="Assign_349">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[hasComma]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">[amount.Contains(",")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="GenAI_Assign_hasDot_38" sap2010:WorkflowViewState.IdRef="Assign_350">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[hasDot]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">[amount.Contains(".")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <If Condition="[hasComma AND hasDot]" sap2010:WorkflowViewState.IdRef="If_35">
                      <If.Then>
                        <If Condition="[amount.LastIndexOf(&quot;,&quot;) &gt; amount.LastIndexOf(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_33">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_130">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_351">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[amount.Replace(".","")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_352">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_353">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[amount.Replace(",","")]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Else>
                        </If>
                      </If.Then>
                      <If.Else>
                        <If Condition="[hasComma AndAlso Not hasDot]" sap2010:WorkflowViewState.IdRef="If_34">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_354">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                      </If.Else>
                    </If>
                    <Assign DisplayName="GenAI_Assign_DictOcrValues_39" sap2010:WorkflowViewState.IdRef="Assign_355">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Object">[Decimal.Parse(amount, System.Globalization.CultureInfo.InvariantCulture).ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                  <Assign x:Key="False" sap2010:WorkflowViewState.IdRef="Assign_153">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Object">[DictOcrValues(strKey)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Object">[item]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Switch>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_152">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Object">[DictOcrValues("PO_NUMBER")]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Object">[CType(String.Join(",", CType(DictOcrValues("PO_NUMBER"), JArray).Select(Function(x) x.ToString())), Object)]</InArgument>
            </Assign.Value>
          </Assign>
          <Sequence DisplayName="GenAI_Sequence_40" sap2010:WorkflowViewState.IdRef="Sequence_59">
            <Assign DisplayName="GenAI_Assign_HeadersJArray_41" sap2010:WorkflowViewState.IdRef="Assign_173">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JArray">[HeadersJArray]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(Jout("Additional_Charges"))]</InArgument>
              </Assign.Value>
            </Assign>
            <ForEach x:TypeArguments="njl:JToken" DisplayName="GenAI_ForEach_42" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[HeadersJArray]">
              <ActivityAction x:TypeArguments="njl:JToken">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                </ActivityAction.Argument>
                <Switch x:TypeArguments="x:Boolean" DisplayName="GenAI_Switch_43" Expression="[item.Count &gt; 1 AndAlso&#xA;    TypeOf item(1) Is JArray AndAlso&#xA;    CType(item(1), JArray).Count &gt; 0 AndAlso&#xA;    {&quot;VAT&quot;, &quot;SHIPPING_AND_HANDLING&quot;, &quot;Invoice_Level_Discount&quot;}.Contains(CType(item(1), JArray)(0).ToString())]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                  <Switch x:TypeArguments="x:String" x:Key="True" DisplayName="GenAI_Switch_44" Expression="[item(1)(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_25">
                    <Sequence x:Key="VAT" DisplayName="GenAI_Sequence_45" sap2010:WorkflowViewState.IdRef="Sequence_114">
                      <Assign DisplayName="GenAI_Assign_DictOcrValues_46" sap2010:WorkflowViewState.IdRef="Assign_284">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_PERCENTAGE")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_PERCENTAGE").ToString),
        If(
            item(1)(1).ToString.Contains("%"),
            item(1)(1).ToString,
            item(1)(1).ToString + "%"
        ),
        DictOcrValues("VAT_PERCENTAGE").ToString + "," + If(
            item(1)(1).ToString.Contains("%"),
            item(1)(1).ToString,
            item(1)(1).ToString + "%"
        )
    ),
    item(1)(1).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="GenAI_Assign_DictOcrValues_47" sap2010:WorkflowViewState.IdRef="Assign_285">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_SUBTOTAL")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("VAT_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iad1:CommentOut DisplayName="GenAI_CommentOut_48" sap2010:WorkflowViewState.IdRef="CommentOut_42">
                        <iad1:CommentOut.Activities>
                          <Assign DisplayName="GenAI_Assign_DictOcrValues_49" sap2010:WorkflowViewState.IdRef="Assign_286">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("VAT_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </iad1:CommentOut.Activities>
                      </iad1:CommentOut>
                      <Sequence DisplayName="GenAI_Sequence_50" sap2010:WorkflowViewState.IdRef="Sequence_133">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Boolean" Name="hasComma" />
                          <Variable x:TypeArguments="x:Boolean" Name="hasDot" />
                          <Variable x:TypeArguments="x:String" Name="amount" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_414">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item(1)(3).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="GenAI_Assign_strTempAmount_51" sap2010:WorkflowViewState.IdRef="Assign_408">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(strTempAmount, "[^\d.,]", "").Trim()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_358">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[strTempAmount]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_359">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[hasComma]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[amount.Contains(",")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_360">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[hasDot]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[amount.Contains(".")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[hasComma AND hasDot]" sap2010:WorkflowViewState.IdRef="If_38">
                          <If.Then>
                            <If Condition="[amount.LastIndexOf(&quot;,&quot;) &gt; amount.LastIndexOf(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_36">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_132">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_361">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[amount.Replace(".","")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_362">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_363">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",","")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Else>
                            </If>
                          </If.Then>
                          <If.Else>
                            <If Condition="[hasComma AndAlso Not hasDot]" sap2010:WorkflowViewState.IdRef="If_37">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_364">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                          </If.Else>
                        </If>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_365">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Decimal.Parse(amount, System.Globalization.CultureInfo.InvariantCulture).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Assign DisplayName="GenAI_Assign_DictOcrValues_52" sap2010:WorkflowViewState.IdRef="Assign_415">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("VAT_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("VAT_AMOUNT").ToString),
        strTempAmount,
        DictOcrValues("VAT_AMOUNT").ToString + "," + strTempAmount
    ),
    strTempAmount
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                    <Sequence x:Key="SHIPPING_AND_HANDLING" DisplayName="GenAI_Sequence_53" sap2010:WorkflowViewState.IdRef="Sequence_115">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_287">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_PERCENTAGE")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("SHIPPING_AND_HANDLING_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_288">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_SUBTOTAL")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("SHIPPING_AND_HANDLING_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iad1:CommentOut DisplayName="GenAI_CommentOut_54" sap2010:WorkflowViewState.IdRef="CommentOut_43">
                        <iad1:CommentOut.Activities>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_289">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </iad1:CommentOut.Activities>
                      </iad1:CommentOut>
                      <Sequence DisplayName="GenAI_Sequence_55" sap2010:WorkflowViewState.IdRef="Sequence_135">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Boolean" Name="hasComma" />
                          <Variable x:TypeArguments="x:Boolean" Name="hasDot" />
                          <Variable x:TypeArguments="x:String" Name="amount" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_416">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item(1)(3).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="GenAI_Assign_strTempAmount_56" sap2010:WorkflowViewState.IdRef="Assign_417">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(strTempAmount, "[^\d.,]", "").Trim()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_403">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[strTempAmount]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_370">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[hasComma]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[amount.Contains(",")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_371">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[hasDot]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[amount.Contains(".")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[hasComma AND hasDot]" sap2010:WorkflowViewState.IdRef="If_41">
                          <If.Then>
                            <If Condition="[amount.LastIndexOf(&quot;,&quot;) &gt; amount.LastIndexOf(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_39">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_134">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_372">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[amount.Replace(".","")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_373">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_374">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",","")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Else>
                            </If>
                          </If.Then>
                          <If.Else>
                            <If Condition="[hasComma AndAlso Not hasDot]" sap2010:WorkflowViewState.IdRef="If_40">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_375">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                          </If.Else>
                        </If>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_376">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Decimal.Parse(amount, System.Globalization.CultureInfo.InvariantCulture).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Assign DisplayName="GenAI_Assign_DictOcrValues_57" sap2010:WorkflowViewState.IdRef="Assign_419">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("SHIPPING_AND_HANDLING_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString),
        strTempAmount,
        DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString + "," + strTempAmount
    ),
    strTempAmount
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                    <Sequence x:Key="Invoice_Level_Discount" DisplayName="GenAI_Sequence_58" sap2010:WorkflowViewState.IdRef="Sequence_116">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_290">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_PERCENTAGE")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("Invoice_Level_Discount_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_SUBTOTAL")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("Invoice_Level_Discount_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iad1:CommentOut DisplayName="GenAI_CommentOut_59" sap2010:WorkflowViewState.IdRef="CommentOut_44">
                        <iad1:CommentOut.Activities>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_AMOUNT")]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </iad1:CommentOut.Activities>
                      </iad1:CommentOut>
                      <Sequence DisplayName="GenAI_Sequence_60" sap2010:WorkflowViewState.IdRef="Sequence_137">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Boolean" Name="hasComma" />
                          <Variable x:TypeArguments="x:Boolean" Name="hasDot" />
                          <Variable x:TypeArguments="x:String" Name="amount" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_420">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item(1)(3).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="GenAI_Assign_strTempAmount_61" sap2010:WorkflowViewState.IdRef="Assign_421">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(strTempAmount, "[^\d.,]", "").Trim()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_422">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[strTempAmount]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_379">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[hasComma]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[amount.Contains(",")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_380">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[hasDot]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[amount.Contains(".")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[hasComma AND hasDot]" sap2010:WorkflowViewState.IdRef="If_44">
                          <If.Then>
                            <If Condition="[amount.LastIndexOf(&quot;,&quot;) &gt; amount.LastIndexOf(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_42">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_136">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_381">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[amount.Replace(".","")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_382">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_383">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",","")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Else>
                            </If>
                          </If.Then>
                          <If.Else>
                            <If Condition="[hasComma AndAlso Not hasDot]" sap2010:WorkflowViewState.IdRef="If_43">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_384">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                          </If.Else>
                        </If>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_385">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strTempAmount]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Decimal.Parse(amount, System.Globalization.CultureInfo.InvariantCulture).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Assign DisplayName="GenAI_Assign_DictOcrValues_62" sap2010:WorkflowViewState.IdRef="Assign_423">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_AMOUNT")]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("Invoice_Level_Discount_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString),
        strTempAmount,
        DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString + "," + strTempAmount
    ),
    strTempAmount
)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </Switch>
                  <TryCatch x:Key="False" DisplayName="GenAI_TryCatch_63" sap2010:WorkflowViewState.IdRef="TryCatch_1">
                    <TryCatch.Try>
                      <Sequence DisplayName="GenAI_Sequence_64" sap2010:WorkflowViewState.IdRef="Sequence_117">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_316">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_PERCENTAGE")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_PERCENTAGE"),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 1,
        DictOcrValues("OtherCharges_PERCENTAGE").ToString() &amp; "," &amp; CType(item(1), JArray)(1).ToString(),
        DictOcrValues("OtherCharges_PERCENTAGE").ToString()
    ),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 1,
        CType(item(1), JArray)(1).ToString(),
        ""
    )
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_317">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_SUBTOTAL")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_SUBTOTAL"),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 2,
        DictOcrValues("OtherCharges_SUBTOTAL").ToString() &amp; "," &amp; CType(item(1), JArray)(2).ToString(),
        DictOcrValues("OtherCharges_SUBTOTAL").ToString()
    ),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 2,
        CType(item(1), JArray)(2).ToString(),
        ""
    )
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_318">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_AMOUNT")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_AMOUNT"),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 3,
        DictOcrValues("OtherCharges_AMOUNT").ToString() &amp; "," &amp; CType(item(1), JArray)(3).ToString(),
        DictOcrValues("OtherCharges_AMOUNT").ToString()
    ),
    If(
        item.Count &gt; 1 AndAlso TypeOf item(1) Is JArray AndAlso CType(item(1), JArray).Count &gt; 3,
        CType(item(1), JArray)(3).ToString(),
        ""
    )
)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad1:CommentOut DisplayName="GenAI_CommentOut_65" sap2010:WorkflowViewState.IdRef="CommentOut_37">
                          <iad1:CommentOut.Activities>
                            <Sequence DisplayName="GenAI_Sequence_66" sap2010:WorkflowViewState.IdRef="Sequence_139">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:Boolean" Name="hasComma" />
                                <Variable x:TypeArguments="x:Boolean" Name="hasDot" />
                                <Variable x:TypeArguments="x:String" Name="amount" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_405">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[DictOcrValues("OtherCharges_AMOUNT").Tostring]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_387">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[hasComma]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">[amount.Contains(",")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_388">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[hasDot]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">[amount.Contains(".")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[hasComma AND hasDot]" sap2010:WorkflowViewState.IdRef="If_47">
                                <If.Then>
                                  <If Condition="[amount.LastIndexOf(&quot;,&quot;) &gt; amount.LastIndexOf(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_45">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_138">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_389">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[amount.Replace(".","")]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_390">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </If.Then>
                                    <If.Else>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_391">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[amount.Replace(",","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Else>
                                  </If>
                                </If.Then>
                                <If.Else>
                                  <If Condition="[hasComma AndAlso Not hasDot]" sap2010:WorkflowViewState.IdRef="If_46">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_392">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                </If.Else>
                              </If>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_393">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_AMOUNT")]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Object">[Decimal.Parse(amount, System.Globalization.CultureInfo.InvariantCulture).ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_293">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_PERCENTAGE")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_PERCENTAGE"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("OtherCharges_PERCENTAGE").ToString),
        item(1)(1).ToString,
        DictOcrValues("OtherCharges_PERCENTAGE").ToString + "," + item(1)(1).ToString
    ),
    item(1)(1).ToString
)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_SUBTOTAL")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_SUBTOTAL"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("OtherCharges_SUBTOTAL").ToString),
        item(1)(2).ToString,
        DictOcrValues("OtherCharges_SUBTOTAL").ToString + "," + item(1)(2).ToString
    ),
    item(1)(2).ToString
)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_295">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("OtherCharges_AMOUNT")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
    DictOcrValues.ContainsKey("OtherCharges_AMOUNT"),
    If(
        String.IsNullOrWhiteSpace(DictOcrValues("OtherCharges_AMOUNT").ToString),
        item(1)(3).ToString,
        DictOcrValues("OtherCharges_AMOUNT").ToString + "," + item(1)(3).ToString
    ),
    item(1)(3).ToString
)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </iad1:CommentOut.Activities>
                        </iad1:CommentOut>
                      </Sequence>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_StudioWriteLine_67" sap2010:WorkflowViewState.IdRef="StudioWriteLine_1" Line="[&quot;Error at Other Additional Charges.&quot;+Environment.NewLine+exception.Message]" />
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                </Switch>
              </ActivityAction>
            </ForEach>
          </Sequence>
        </Sequence>
        <Sequence DisplayName="GenAI_Sequence_68" sap2010:WorkflowViewState.IdRef="Sequence_58">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
            <Variable x:TypeArguments="njl:JArray" Name="detailValuesJArray" />
          </Sequence.Variables>
          <Assign DisplayName="GenAI_Assign_LinesJarray_69" sap2010:WorkflowViewState.IdRef="Assign_129">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JArray">[LinesJarray]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(Jout("Lines"))]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="GenAI_Assign_intLineDictCounter_70" sap2010:WorkflowViewState.IdRef="Assign_224">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[intLineDictCounter]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="njl:JToken" DisplayName="GenAI_ForEach_71" sap2010:WorkflowViewState.IdRef="ForEach`1_18" Values="[LinesJarray]">
            <ActivityAction x:TypeArguments="njl:JToken">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="njl:JToken" Name="PoNO" />
              </ActivityAction.Argument>
              <ForEach x:TypeArguments="njl:JProperty" DisplayName="GenAI_ForEach_72" sap2010:WorkflowViewState.IdRef="ForEach`1_23" Values="[CType(PoNO, JObject).Properties]">
                <ActivityAction x:TypeArguments="njl:JProperty">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="njl:JProperty" Name="Line" />
                  </ActivityAction.Argument>
                  <Switch x:TypeArguments="x:String" DisplayName="GenAI_Switch_73" Expression="[Line.Name]" sap2010:WorkflowViewState.IdRef="Switch`1_27">
                    <Assign x:Key="PO_Number" sap2010:WorkflowViewState.IdRef="Assign_297">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[strPONumber]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Line.Value.Tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Sequence x:Key="Details" sap2010:Annotation.AnnotationText="Details contains all lines and 11 fields data from each line" DisplayName="GenAI_Sequence_74" sap2010:WorkflowViewState.IdRef="Sequence_120">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="njl:JArray" Name="JADetail" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JArray">[detailValuesJArray]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JArray">[JArray.FromObject(PoNO("Details"))]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="GenAI_ForEach_75" sap2010:WorkflowViewState.IdRef="ForEach`1_34" Values="[detailValuesJArray]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="detail" />
                          </ActivityAction.Argument>
                          <Sequence DisplayName="GenAI_Sequence_76" sap2010:WorkflowViewState.IdRef="Sequence_119">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="strTempDetail" />
                            </Sequence.Variables>
                             <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strTempDetail]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[detail.ToString().Replace("'", "")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JArray">[JADetail]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(strTempDetail)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_301">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">0</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"SUPPLIER_ITEM_CODE", ""},
    {"CUSTOMER_ITEM_CODE", ""},
    {"DESCRIPTION", ""},
    {"ITEM_STATUS", ""},
    {"QUANTITY", ""},
    {"UNIT_PRICE", ""},
    {"LINE_LEVEL_ITEM_DISCOUNT", ""},
    {"UNIT_OF_MEASURE", ""},
    {"LINE_AMOUNT", ""},
    {"WEIGHT_OR_CARTONQUANTITY", ""},
    {"DELIVERY_NOTE_DATA", ""},
    {"PO_Number", ""},
    {"PO_Total_Amount", ""},
    {"DELIVERY_NOTE_NUMBER", ""}
}]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="GenAI_ForEach_77" sap2010:WorkflowViewState.IdRef="ForEach`1_33" Values="[JADetail]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="element" />
                                </ActivityAction.Argument>
                                <Sequence DisplayName="GenAI_Sequence_78" sap2010:WorkflowViewState.IdRef="Sequence_118">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_303">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[New String() { "SUPPLIER_ITEM_CODE", "CUSTOMER_ITEM_CODE", "DESCRIPTION", "ITEM_STATUS", "QUANTITY", "UNIT_PRICE", "LINE_LEVEL_ITEM_DISCOUNT", "UNIT_OF_MEASURE", "LINE_AMOUNT", "WEIGHT_OR_CARTONQUANTITY", "DELIVERY_NOTE_DATA" }(i)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Switch x:TypeArguments="x:Boolean" DisplayName="GenAI_Switch_79" Expression="[ListCommaDotKeys.Contains(strKey)]" sap2010:WorkflowViewState.IdRef="Switch`1_30">
                                    <Sequence x:Key="True" DisplayName="GenAI_Sequence_80" sap2010:WorkflowViewState.IdRef="Sequence_141">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Boolean" Name="hasComma" />
                                        <Variable x:TypeArguments="x:Boolean" Name="hasDot" />
                                        <Variable x:TypeArguments="x:String" Name="amount" />
                                      </Sequence.Variables>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_394">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Object">[LinesDict(strKey)]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Object">[element]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign DisplayName="GenAI_Assign_LinesDict_81" sap2010:WorkflowViewState.IdRef="Assign_411">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Object">[LinesDict(strKey)]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[Regex.Replace(LinesDict(strKey).Tostring, "[^\d.,]", "").Trim()]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_406">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[LinesDict(strKey).Tostring]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_396">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[hasComma]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">[amount.Contains(",")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_397">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[hasDot]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">[amount.Contains(".")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <If Condition="[hasComma AND hasDot]" sap2010:WorkflowViewState.IdRef="If_50">
                                        <If.Then>
                                          <If Condition="[amount.LastIndexOf(&quot;,&quot;) &gt; amount.LastIndexOf(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_48">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_140">
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_398">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[amount.Replace(".","")]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_399">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_400">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[amount.Replace(",","")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Else>
                                          </If>
                                        </If.Then>
                                        <If.Else>
                                          <If Condition="[hasComma AndAlso Not hasDot]" sap2010:WorkflowViewState.IdRef="If_49">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_401">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[amount]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[amount.Replace(",",".")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                        </If.Else>
                                      </If>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_402">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Object">[LinesDict(strKey)]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Object">[Decimal.Parse(amount, System.Globalization.CultureInfo.InvariantCulture).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                    <Assign x:Key="False" sap2010:WorkflowViewState.IdRef="Assign_320">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Object">[LinesDict(strKey)]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Object">[element]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Switch>
                                  <Assign DisplayName="GenAI_Assign_LinesDict_82" sap2010:WorkflowViewState.IdRef="Assign_304">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strKey = "DELIVERY_NOTE_DATA" AndAlso CType(LinesDict(strKey), JArray)(0).ToString &lt;&gt; "''",
     CType(LinesDict(strKey), JArray)(0).ToString,LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="GenAI_Assign_LinesDict_83" sap2010:WorkflowViewState.IdRef="Assign_324">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[If(strKey = "DELIVERY_NOTE_DATA" AndAlso LinesDict("DELIVERY_NOTE_NUMBER").ToString = "''", "",LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="GenAI_Assign_LinesDict_84" sap2010:WorkflowViewState.IdRef="Assign_325">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("PO_Number")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[If(strKey = "PO_Number" AndAlso LinesDict("PO_Number").ToString = "''", "",LinesDict("PO_Number").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[strDeliveryNote]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strKey = "DELIVERY_NOTE_DATA" AndAlso CType(LinesDict(strKey), JArray)(0).ToString &lt;&gt; "''",
    If(String.IsNullOrWhiteSpace(strDeliveryNote),
        CType(LinesDict(strKey), JArray)(0).ToString,
        strDeliveryNote + "," + CType(LinesDict(strKey), JArray)(0).ToString),
    strDeliveryNote)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <iad1:CommentOut DisplayName="GenAI_CommentOut_85" sap2010:WorkflowViewState.IdRef="CommentOut_38">
                                    <iad1:CommentOut.Activities>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_319">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[strDeliveryNote]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strKey = "DELIVERY_NOTE_DATA", 
	If(String.IsNullOrWhiteSpace(strDeliveryNote),
		CType(LinesDict(strKey), JArray)(0).ToString,
	strDeliveryNote + ","+ CType(LinesDict(strKey), JArray)(0).ToString), strDeliveryNote)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </iad1:CommentOut.Activities>
                                  </iad1:CommentOut>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_305">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_29" MethodName="Add">
                              <InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListOcrLineValues]</InArgument>
                              </InvokeMethod.TargetObject>
                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                            </InvokeMethod>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                    <Sequence x:Key="PO_Total_Amount" DisplayName="GenAI_Sequence_86" sap2010:WorkflowViewState.IdRef="Sequence_122">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Int32" Name="intCounter" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_306">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[intCounter]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[(ListOcrLineValues.count-1)-(detailValuesJArray.count-1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="x:Int32" DisplayName="GenAI_ForEach_87" sap2010:WorkflowViewState.IdRef="ForEach`1_35" Values="[Enumerable.Range(intCounter, detailValuesJArray.count)]">
                        <ActivityAction x:TypeArguments="x:Int32">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="x:Int32" Name="DictIndex" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:Annotation.AnnotationText="This block will Add PO Number &amp; PO Total Amount to all lines within that PO Data" DisplayName="GenAI_Sequence_88" sap2010:WorkflowViewState.IdRef="Sequence_121">
                            <Assign DisplayName="GenAI_Assign_ListOcrLineValues_89" sap2010:WorkflowViewState.IdRef="Assign_307">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[ListOcrLineValues(DictIndex)("PO_Number")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[strPONumber]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="GenAI_Assign_ListOcrLineValues_90" sap2010:WorkflowViewState.IdRef="Assign_308">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[ListOcrLineValues(DictIndex)("PO_Total_Amount")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[Line.Value.Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="GenAI_Assign_intLineDictCounter_91" sap2010:WorkflowViewState.IdRef="Assign_309">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[intLineDictCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[intLineDictCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                  </Switch>
                </ActivityAction>
              </ForEach>
            </ActivityAction>
          </ForEach>
          <iad1:CommentOut DisplayName="GenAI_CommentOut_92" sap2010:WorkflowViewState.IdRef="CommentOut_31">
            <iad1:CommentOut.Activities>
              <ForEach x:TypeArguments="x:Object" DisplayName="GenAI_ForEach_93" sap2010:WorkflowViewState.IdRef="ForEach`1_30" Values="[ListOcrLineValues]">
                <ActivityAction x:TypeArguments="x:Object">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:Object" Name="item" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_93">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="tempdict" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_233">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[tempdict]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[CType(item, Dictionary(Of String, Object))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:KeyValuePair(x:String, x:Object)" DisplayName="GenAI_ForEach_94" sap2010:WorkflowViewState.IdRef="ForEach`1_32" Values="[tempdict]">
                      <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, x:Object)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, x:Object)" Name="item" />
                        </ActivityAction.Argument>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_Append_Line_95" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Key is - &quot;+item.key+&quot;      -      Value is - &quot;+item.value.ToString]" Source="C:\Users\<USER>\Downloads\DictCountLog.txt" />
                      </ActivityAction>
                    </ForEach>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </iad1:CommentOut.Activities>
          </iad1:CommentOut>
        </Sequence>
        <Assign DisplayName="GenAI_Assign_DictOcrValues_96" sap2010:WorkflowViewState.IdRef="Assign_311">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("DELIVERY_NOTE_DATA")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Object">[strDeliveryNote]</InArgument>
          </Assign.Value>
        </Assign>
      </Sequence>
      <Sequence x:Key="False" DisplayName="GenAI_Sequence_97" sap2010:WorkflowViewState.IdRef="Sequence_123">
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="GenAI_Append_Line_98" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[&quot;Exception occurred in Get AI XAML. genAIRespCode is: &quot;+genAIRespCode.ToString]" Source="[logFile]" />
        <Throw Exception="[New Exception(&quot;Exception occurred in Get AI XAML.&quot;)]" sap2010:WorkflowViewState.IdRef="Throw_1" />
      </Sequence>
    </Switch>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_347" sap:VirtualizedContainerService.HintSize="786,65.3333333333333" />
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="OpenBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="NavigateTo_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_1" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_3" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="ScreenShot_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="FileToBase64_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="CloseBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Read_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_322" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_323" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_41" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_326" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_327" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_328" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_329" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_330" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_314" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="786,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="744,62" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="744,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="744,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_7" sap:VirtualizedContainerService.HintSize="744,22" />
      <sap2010:ViewStateData Id="MessageBox_40" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_40" sap:VirtualizedContainerService.HintSize="744,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="722,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="722,62" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_356" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_407" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_348" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_349" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_350" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_351" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_352" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_353" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_354" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_355" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Switch`1_29" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="498.666666666667,500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="722,652.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="722,62" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="700,62" />
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_42" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_414" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_408" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_358" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_359" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_360" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_361" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_362" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_363" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="532,442" />
      <sap2010:ViewStateData Id="Assign_364" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_365" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_415" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="264,590.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_287" sap:VirtualizedContainerService.HintSize="264,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_288" sap:VirtualizedContainerService.HintSize="264,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_289" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_43" sap:VirtualizedContainerService.HintSize="264,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_416" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_417" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_403" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_370" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_371" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_372" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_373" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_374" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="532,442" />
      <sap2010:ViewStateData Id="Assign_375" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_376" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,788.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_419" sap:VirtualizedContainerService.HintSize="264,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="286,1326.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_290" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_44" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_420" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_421" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_422" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_379" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_380" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_381" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_382" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_383" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="532,442" />
      <sap2010:ViewStateData Id="Assign_384" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_385" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_137" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_423" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="264,590.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_25" sap:VirtualizedContainerService.HintSize="476.666666666667,828.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_316" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_317" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_318" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_405" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_387" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_388" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_389" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_390" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_138" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_391" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="532,442" />
      <sap2010:ViewStateData Id="Assign_392" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_393" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_295" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_37" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="StudioWriteLine_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="669.333333333333,1046.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="700,1199.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="722,1425.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="744,2548">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="538,62" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="538,62" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="582,62" />
      <sap2010:ViewStateData Id="MessageBox_36" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_33" sap:VirtualizedContainerService.HintSize="582,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="MessageBox_37" sap:VirtualizedContainerService.HintSize="200,63.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_34" sap:VirtualizedContainerService.HintSize="529.333333333333,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="MessageBox_38" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_35" sap:VirtualizedContainerService.HintSize="529.333333333333,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="529.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_394" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_411" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_406" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_396" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_397" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_398" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_399" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_400" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="532,442" />
      <sap2010:ViewStateData Id="Assign_401" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_402" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="264,788.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_320" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Switch`1_30" sap:VirtualizedContainerService.HintSize="476.666666666667,1003.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_324" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_325" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_319" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_38" sap:VirtualizedContainerService.HintSize="476.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="498.666666666667,1844">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_33" sap:VirtualizedContainerService.HintSize="529.333333333333,1996.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_29" sap:VirtualizedContainerService.HintSize="529.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="551.333333333333,2902">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_34" sap:VirtualizedContainerService.HintSize="582,3054.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="604,3403.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="264,444">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_35" sap:VirtualizedContainerService.HintSize="294.666666666667,596.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="316.666666666667,818">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_27" sap:VirtualizedContainerService.HintSize="476.666666666667,192">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_23" sap:VirtualizedContainerService.HintSize="507.333333333333,344.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_18" sap:VirtualizedContainerService.HintSize="538,497.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_233" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_32" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667" />
      <sap2010:ViewStateData Id="Sequence_93" sap:VirtualizedContainerService.HintSize="306.666666666667,438.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_30" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_31" sap:VirtualizedContainerService.HintSize="538,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="744,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="744,62" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="766,3332.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Throw_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_28" sap:VirtualizedContainerService.HintSize="786,3547.33333333333" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="808,3910.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="848,4230.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
