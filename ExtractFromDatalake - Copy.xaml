﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="maxNotReceivedCount" Type="InArgument(x:Int32)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="Attribute_List" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalList" Type="InOutArgument(scg:List(x:String))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Linq</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ExtractFromDatalake - Copy_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="query" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
      <Variable x:TypeArguments="x:Int32" Name="status1" />
      <Variable x:TypeArguments="x:String" Name="jobid" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp2" />
      <Variable x:TypeArguments="x:Int32" Name="status2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp3" />
      <Variable x:TypeArguments="x:Int32" Name="status3" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:Boolean" Name="fileExist" />
      <Variable x:TypeArguments="x:String" Name="vendorId" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="approvalLists" />
      <Variable x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))" Name="ListOcrLineValues" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="Dictline" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="x:String" Name="strKey" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of String)]</InArgument>
      </Assign.Value>
    </Assign>
    <iad:CommentOut DisplayName="ExtractFromDatalake - Copy_CommentOut_2" sap2010:WorkflowViewState.IdRef="CommentOut_3">
      <iad:CommentOut.Activities>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[query]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">["SELECT H.RPA_Process_ID, H.Status, H.Comments, H.Last_Run_Time, H.Process_Type, H.Name, H.ERP, H.Failure_Count, H.Variation_ID AS Header_Variation_ID, H.Updated_By, H.Additional1, H.Additional2, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_3' THEN A.AttributeValue END), '') AS Additional_Attribute_3, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_2' THEN A.AttributeValue END), '') AS Additional_Attribute_2, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_1' THEN A.AttributeValue END), '') AS Additional_Attribute_1, COALESCE(MAX(CASE WHEN A.AttributeName = 'Comments' THEN A.AttributeValue END), '') AS Attribute_Comments, COALESCE(MAX(CASE WHEN A.AttributeName = 'Vendor_Name' THEN A.AttributeValue END), '') AS Vendor_Name, COALESCE(MAX(CASE WHEN A.AttributeName = 'Vendor_ID' THEN A.AttributeValue END), '') AS Vendor_ID, COALESCE(MAX(CASE WHEN A.AttributeName = 'Company' THEN A.AttributeValue END), '') AS Company, COALESCE(MAX(CASE WHEN A.AttributeName = 'DeliveryNote_Number' THEN A.AttributeValue END), '') AS DeliveryNote_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Discount_Amount' THEN A.AttributeValue END), '0') AS Discount_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Shipping_Charges' THEN A.AttributeValue END), '0') AS Shipping_Charges, COALESCE(MAX(CASE WHEN A.AttributeName = 'Tax_Amount' THEN A.AttributeValue END), '0') AS Tax_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Total_Amount' THEN A.AttributeValue END), '0') AS Total_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Subtotal_Amount' THEN A.AttributeValue END), '0') AS Subtotal_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Date' THEN A.AttributeValue END), '') AS Invoice_Date, COALESCE(MAX(CASE WHEN A.AttributeName = 'PO_Number' THEN A.AttributeValue END), '') AS PO_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Number' THEN A.AttributeValue END), '') AS Invoice_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Type' THEN A.AttributeValue END), '') AS Invoice_Type, COALESCE(MAX(CASE WHEN A.AttributeName = 'File_Name' THEN A.AttributeValue END), '') AS File_Name, COALESCE(MAX(CASE WHEN A.AttributeName = 'GLCode' THEN A.AttributeValue END), '') AS GLCode, COALESCE(MAX(CASE WHEN A.AttributeName = 'Division' THEN A.AttributeValue END), '') AS Division,COALESCE(MAX(CASE WHEN A.AttributeName = 'APResp' THEN A.AttributeValue END), '') AS APResp, COALESCE(MAX(CASE WHEN A.AttributeName = 'Email_Received_Time' THEN A.AttributeValue END), '') AS Email_Received_Time, COALESCE(MAX(CASE WHEN A.AttributeName = 'Email_Subject' THEN A.AttributeValue END), '') AS Email_Subject, COALESCE(MAX(L.Data), '') AS Line_Data FROM RPA_Process_Header H JOIN RPA_Process_Attributes A ON H.RPA_Process_ID = A.Process_ID LEFT JOIN RPA_Process_Lines L ON H.RPA_Process_ID = L.RPA_Process_ID WHERE H.ERP = '"+miscValues("ERP").ToString+"' AND NOT H.Status = 'ARCHIVE' AND H.process_type = 'INVOICE PROCESSING' AND ((H.Status = 'MISSINGINFORMATION' AND H.Updated_By = 'Y') OR (H.Status = 'NEEDSVERIFICATION' AND H.Updated_By = 'Y') OR (H.Status = 'PONOTRECEIVED' AND (H.Updated_By = 'Y' OR TO_DATE(H.Last_Run_Time, 'YYYY-MM-DD') &lt; CURRENT_DATE) AND Failure_Count &lt; " + maxNotReceivedCount.ToString + ")) GROUP BY H.RPA_Process_ID, H.Status, H.Comments, H.Last_Run_Time, H.Process_Type, H.Name, H.ERP, H.Failure_Count, H.Variation_ID, H.Updated_By, H.Additional1, H.Additional2 ORDER BY H.Last_Run_Time DESC"]</InArgument>
          </Assign.Value>
        </Assign>
      </iad:CommentOut.Activities>
    </iad:CommentOut>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[query]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["SELECT H.RPA_Process_ID, H.Status, H.Comments, H.Last_Run_Time, H.Process_Type, H.Name, H.ERP, H.Failure_Count, H.Variation_ID AS Header_Variation_ID, H.Updated_By, H.Additional1, H.Additional2, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_3' THEN A.AttributeValue END), '') AS Additional_Attribute_3, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_2' THEN A.AttributeValue END), '') AS Additional_Attribute_2, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_1' THEN A.AttributeValue END), '') AS Additional_Attribute_1, COALESCE(MAX(CASE WHEN A.AttributeName = 'Comments' THEN A.AttributeValue END), '') AS Attribute_Comments, COALESCE(MAX(CASE WHEN A.AttributeName = 'Vendor_Name' THEN A.AttributeValue END), '') AS Vendor_Name, COALESCE(MAX(CASE WHEN A.AttributeName = 'Vendor_ID' THEN A.AttributeValue END), '') AS Vendor_ID, COALESCE(MAX(CASE WHEN A.AttributeName = 'Company' THEN A.AttributeValue END), '') AS Company, COALESCE(MAX(CASE WHEN A.AttributeName = 'DeliveryNote_Number' THEN A.AttributeValue END), '') AS DeliveryNote_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Discount_Amount' THEN A.AttributeValue END), '0') AS Discount_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Shipping_Charges' THEN A.AttributeValue END), '0') AS Shipping_Charges, COALESCE(MAX(CASE WHEN A.AttributeName = 'Tax_Amount' THEN A.AttributeValue END), '0') AS Tax_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Total_Amount' THEN A.AttributeValue END), '0') AS Total_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Subtotal_Amount' THEN A.AttributeValue END), '0') AS Subtotal_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Date' THEN A.AttributeValue END), '') AS Invoice_Date, COALESCE(MAX(CASE WHEN A.AttributeName = 'PO_Number' THEN A.AttributeValue END), '') AS PO_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Number' THEN A.AttributeValue END), '') AS Invoice_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Type' THEN A.AttributeValue END), '') AS Invoice_Type, COALESCE(MAX(CASE WHEN A.AttributeName = 'File_Name' THEN A.AttributeValue END), '') AS File_Name, COALESCE(MAX(CASE WHEN A.AttributeName = 'GLCode' THEN A.AttributeValue END), '') AS GLCode, COALESCE(MAX(CASE WHEN A.AttributeName = 'Division' THEN A.AttributeValue END), '') AS Division,COALESCE(MAX(CASE WHEN A.AttributeName = 'APResp' THEN A.AttributeValue END), '') AS APResp, COALESCE(MAX(CASE WHEN A.AttributeName = 'Email_Received_Time' THEN A.AttributeValue END), '') AS Email_Received_Time, COALESCE(MAX(CASE WHEN A.AttributeName = 'Email_Subject' THEN A.AttributeValue END), '') AS Email_Subject, COALESCE(MAX(L.Data), '') AS Line_Data FROM RPA_Process_Header H JOIN RPA_Process_Attributes A ON H.RPA_Process_ID = A.Process_ID LEFT JOIN RPA_Process_Lines L ON H.RPA_Process_ID = L.RPA_Process_ID WHERE H.ERP = '"+miscValues("ERP").ToString+"' AND NOT H.Status = 'ARCHIVE' AND H.process_type = 'INVOICE PROCESSING' AND ((H.Status = 'MISSINGINFORMATION' AND H.Updated_By = 'Y') OR (H.Status = 'NEEDSVERIFICATION' AND H.Updated_By = 'Y') OR (H.Status = 'PONOTRECEIVED' OR H.Updated_By = 'Y' AND Failure_Count &lt; " + maxNotReceivedCount.ToString + ")) GROUP BY H.RPA_Process_ID, H.Status, H.Comments, H.Last_Run_Time, H.Process_Type, H.Name, H.ERP, H.Failure_Count, H.Variation_ID, H.Updated_By, H.Additional1, H.Additional2 ORDER BY H.Last_Run_Time DESC"]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" ResponseCode="{x:Null}" ContentType="text_plain" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_IONAPIRequestWizard_3" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" PostData="[query]" Response="[resp1]" StatusCode="[status1]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/?records=0&quot;]">
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="0" />
          <scg:List x:TypeArguments="x:String" Capacity="0" />
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[status1 = 200 or status1 = 202 OR status1 = 201]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="division" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="accountingDimArr" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[jobid]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[JToken.Parse(resp1.ReadAsText)("queryId").ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <Delay Duration="00:00:10" sap2010:WorkflowViewState.IdRef="Delay_1" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_IONAPIRequestWizard_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[resp2]" StatusCode="[status2]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/status/?timeout=0&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>queryid</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>str1</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[status2 = 200 OR status2 = 202 OR status2 = 201]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="variationID" />
                  <Variable x:TypeArguments="x:String" Name="statusComments" />
                  <Variable x:TypeArguments="x:String" Name="status" />
                  <Variable x:TypeArguments="x:String" Name="failureCountStr" />
                </Sequence.Variables>
                <While sap2010:WorkflowViewState.IdRef="While_1" Condition="[JToken.Parse(resp2.ReadAsText)(&quot;status&quot;).ToString = &quot;RUNNING&quot;]">
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                    <Delay Duration="00:00:10" sap2010:WorkflowViewState.IdRef="Delay_2" />
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_IONAPIRequestWizard_5" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[resp2]" StatusCode="[status2]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/status/?timeout=0&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>queryid</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>str1</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                  </Sequence>
                </While>
                <If Condition="[JToken.Parse(resp2.ReadAsText)(&quot;status&quot;).ToString = &quot;FINISHED&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="text_plain" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[resp3]" StatusCode="[status3]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/result/?offset=0&amp;limit=10000&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="0" />
                            <scg:List x:TypeArguments="x:String" Capacity="0" />
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                    </Sequence>
                  </If.Then>
                </If>
                <If Condition="[status3 = 200 OR status3 = 202 OR status3 = 201]" sap2010:WorkflowViewState.IdRef="If_3">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="s:DateTime" Name="today" />
                        <Variable x:TypeArguments="s:DateTime" Name="dt1" />
                        <Variable x:TypeArguments="x:String" Name="lastRunTime" />
                        <Variable x:TypeArguments="x:Int32" Name="failureCount" />
                        <Variable x:TypeArguments="x:String" Name="vendorName" />
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
                        <Variable x:TypeArguments="x:String" Name="accountingDim" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(resp3.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ExtractFromDatalake - Copy_ForEach_7" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[out1]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="emailSubject" />
                              <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                              <Variable x:TypeArguments="x:String" Name="processId" />
                              <Variable x:TypeArguments="x:String" Name="fileName" />
                              <Variable x:TypeArguments="x:Int32" Name="processPoResponseCode" />
                              <Variable x:TypeArguments="x:String" Name="InvoiceType" />
                              <Variable x:TypeArguments="x:String" Name="GLCode" />
                              <Variable x:TypeArguments="x:String" Name="comments" />
                              <Variable x:TypeArguments="x:String" Name="DLComments" />
                              <Variable x:TypeArguments="x:String" Name="APResp" />
                              <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="DictOcrValues" />
                              <Variable x:TypeArguments="x:Int32" Name="intLogLinesCounter" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of String)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[accountingDim]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Email_Subject").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Email_Received_Time").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Vendor_ID").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Vendor_Name").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_8" sap2010:WorkflowViewState.IdRef="Assign_136">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[DictOcrValues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" xml:space="preserve">[New Dictionary(Of String, Object) From {
    {"INVOICE_RECEIPT_ID", ""},
    {"INVOICE_RECEIPT_DATE", ""},
    {"TOTAL", ""},
    {"SUBTOTAL", ""},
    {"PO_NUMBER", ""},
    {"VENDOR_NAME", ""},
    {"VENDOR_ADDRESS", ""},
    {"VENDOR_PHONE", ""},
    {"SHIP_TO_ADDRESS", ""},
    {"BILL_TO_ADDRESS", ""},
    {"ISO_CURRENCY_CODE", ""},
    {"REFERENCE", ""},
    {"UNIQUE_REGISTRATION_CODE", ""},
    {"DOCUMENT_CLASS", ""},
    {"REMIT_TO_NAME", ""},
    {"REMIT_TO_ADDRESS", ""},
    {"VAT_PERCENTAGE", ""},
    {"VAT_SUBTOTAL", ""},
    {"VAT_AMOUNT", ""},
    {"SHIPPING_AND_HANDLING_PERCENTAGE", ""},
    {"SHIPPING_AND_HANDLING_SUBTOTAL", ""},
    {"SHIPPING_AND_HANDLING_AMOUNT", ""},
    {"Invoice_Level_Discount_PERCENTAGE", ""},
    {"Invoice_Level_Discount_SUBTOTAL", ""},
    {"Invoice_Level_Discount_AMOUNT", ""},
    {"OtherCharges_PERCENTAGE", ""},
    {"OtherCharges_SUBTOTAL", ""},
    {"OtherCharges_AMOUNT", ""},
    {"DELIVERY_NOTE_DATA", ""},
    {"IBAN",""},
    {"EXCEPTION_CATEGORY",""}
}]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="ExtractFromDatalake - Copy_Assign_ListOcrLineValues_9" sap2010:WorkflowViewState.IdRef="Assign_137">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListOcrLineValues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String,Object))]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("INVOICE_RECEIPT_ID")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Invoice_Number").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("INVOICE_RECEIPT_DATE")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Invoice_Date").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("TOTAL")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Total_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Subtotal_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("PO_NUMBER")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("PO_Number").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[item(&quot;Tax_Amount&quot;).ToString.contains(&quot;|&quot;)]" sap2010:WorkflowViewState.IdRef="If_46">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_51">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:Int32" Name="TaxCount" />
                                    <Variable x:TypeArguments="x:Int32" Name="j" />
                                  </Sequence.Variables>
                                  <Assign DisplayName="ExtractFromDatalake - Copy_Assign_TaxCount_10" sap2010:WorkflowViewState.IdRef="Assign_147">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[TaxCount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[item("Tax_Amount").ToString.Split("|"c).count]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="ExtractFromDatalake - Copy_Assign_j_11" sap2010:WorkflowViewState.IdRef="Assign_148">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[j]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <While sap2010:WorkflowViewState.IdRef="While_2" Condition="[j &lt; TaxCount]">
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
                                      <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_12" sap2010:WorkflowViewState.IdRef="Assign_150">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_PERCENTAGE")]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_PERCENTAGE").ToString + "," + item("Tax_Amount").ToString.Split("|"c)(j).split(","c)(0)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_13" sap2010:WorkflowViewState.IdRef="Assign_151">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").ToString + "," + item("Tax_Amount").ToString.Split("|"c)(j).split(","c)(1)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign DisplayName="ExtractFromDatalake - Copy_Assign_j_14" sap2010:WorkflowViewState.IdRef="Assign_149">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Int32">[j]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Int32">[j+1]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </While>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_52">
                                  <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_15" sap2010:WorkflowViewState.IdRef="Assign_144">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_PERCENTAGE")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item("Tax_Amount").ToString().Split(","c)(0)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_16" sap2010:WorkflowViewState.IdRef="Assign_145">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Object">[item("Tax_Amount").ToString().Split(","c)(1)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Else>
                            </If>
                            <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_17" sap2010:WorkflowViewState.IdRef="Assign_152">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_PERCENTAGE")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_PERCENTAGE").ToString.Trim(","c)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_18" sap2010:WorkflowViewState.IdRef="Assign_153">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").ToString.Trim(","c)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Shipping_Charges").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_AMOUNT")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Discount_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_22">
                              <If.Then>
                                <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_19" sap2010:WorkflowViewState.IdRef="Assign_65">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VAT_AMOUNT")]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Object">["0"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <If Condition="[DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_23">
                              <If.Then>
                                <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_20" sap2010:WorkflowViewState.IdRef="Assign_66">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Object">["0"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <If Condition="[DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_28">
                              <If.Then>
                                <Assign DisplayName="ExtractFromDatalake - Copy_Assign_DictOcrValues_21" sap2010:WorkflowViewState.IdRef="Assign_74">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Object">[DictOcrValues("Invoice_Level_Discount_AMOUNT")]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Object">["0"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <iad:CommentOut DisplayName="ExtractFromDatalake - Copy_CommentOut_22" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                              <iad:CommentOut.Activities>
                                <If Condition="[Math.abs(Convert.ToDecimal(DictOcrValues(&quot;TOTAL&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;SUBTOTAL&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString)) &lt; 1]" sap2010:WorkflowViewState.IdRef="If_32">
                                  <If.Else>
                                    <If Condition="[Convert.ToDecimal(DictOcrValues(&quot;TOTAL&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;SUBTOTAL&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString) - Convert.ToDecimal(DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString) + Convert.ToDecimal(DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString) &lt; 1]" sap2010:WorkflowViewState.IdRef="If_31">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Object">[(Convert.ToDecimal(DictOcrValues("TOTAL").ToString) - Convert.ToDecimal(DictOcrValues("SUBTOTAL").ToString) - Convert.ToDecimal(DictOcrValues("VAT_AMOUNT").ToString)).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                      <If.Else>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT")]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Object">[(Convert.ToDecimal(DictOcrValues("TOTAL").ToString) - Convert.ToDecimal(DictOcrValues("SUBTOTAL").ToString) - Convert.ToDecimal(DictOcrValues("VAT_AMOUNT").ToString)).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Else>
                                    </If>
                                  </If.Else>
                                </If>
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("VENDOR_NAME")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Vendor_Name").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("DELIVERY_NOTE_DATA")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("DeliveryNote_Number").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Invoice_Type").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("File_Name").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Division").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[failureCountStr]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Failure_Count").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[DLComments]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Attribute_Comments").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_154">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[DLComments]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace( DLComments.Trim,"\n","\\n")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[miscValues("Comments")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[DLComments]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_160">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[miscValues("addInfo")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Additional_Attribute_3").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[DictOcrValues("EXCEPTION_CATEGORY")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Status").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("APResp").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[accountingDim]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("Line_Data").ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(x:String)">[accountingDimArr]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[accountingDim.Split(New String() {Microsoft.VisualBasic.ControlChars.Lf}, StringSplitOptions.RemoveEmptyEntries).ToList]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[accountingDimArr.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_30">
                                <If.Then>
                                  <ForEach x:TypeArguments="x:Object" DisplayName="ExtractFromDatalake - Copy_ForEach_23" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[accountingDimArr]">
                                    <ActivityAction x:TypeArguments="x:Object">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="x:Object" Name="item" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[Dictline]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ForEach x:TypeArguments="x:String" DisplayName="ExtractFromDatalake - Copy_ForEach_24" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[item.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)]">
                                          <ActivityAction x:TypeArguments="x:String">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                                              <iad:CommentOut DisplayName="ExtractFromDatalake - Copy_CommentOut_25" sap2010:WorkflowViewState.IdRef="CommentOut_7">
                                                <iad:CommentOut.Activities>
                                                  <sco:Collection x:TypeArguments="Activity" />
                                                </iad:CommentOut.Activities>
                                              </iad:CommentOut>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[New String() {
    "SUPPLIER_ITEM_CODE",
    "DESCRIPTION",
    "QUANTITY",
    "UNIT_PRICE",
    "LINE_AMOUNT",
    "STATUS",
    "VAT_CODE",
    "RECONCILE_CODE",
	"VOUCHER_TEXT",
 	"COSTING_ELEMENT",
	"ACTIVITY"
}(i)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Object">[Dictline(strKey)]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Object">[item2]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                                                <TryCatch.Try>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Object">[Dictline("PO_Number")]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Object">[Dictline("SUPPLIER_ITEM_CODE").ToString.Split(","c)(1).split("/"c)(0)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </TryCatch.Try>
                                                <TryCatch.Catches>
                                                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                                                    <ActivityAction x:TypeArguments="s:Exception">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                      </ActivityAction.Argument>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:Object">[Dictline("PO_Number")]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </ActivityAction>
                                                  </Catch>
                                                </TryCatch.Catches>
                                              </TryCatch>
                                              <iad:CommentOut DisplayName="ExtractFromDatalake - Copy_CommentOut_26" sap2010:WorkflowViewState.IdRef="CommentOut_5">
                                                <iad:CommentOut.Activities>
                                                  <sco:Collection x:TypeArguments="Activity" />
                                                </iad:CommentOut.Activities>
                                              </iad:CommentOut>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[Dictline]</InArgument>
                                        </InvokeMethod>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                </If.Then>
                              </If>
                            </Sequence>
                            <iad:CommentOut DisplayName="ExtractFromDatalake - Copy_CommentOut_27" sap2010:WorkflowViewState.IdRef="CommentOut_6">
                              <iad:CommentOut.Activities>
                                <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ExtractFromDatalake - Copy_ForEach_28" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[ListocrLineValues]">
                                  <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_54" />
                                  </ActivityAction>
                                </ForEach>
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_Path_Validate_29" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[fileExist]" Path="[configurationFolder+&quot;\Failure\&quot;+fileName]" />
                            <If Condition="[item(&quot;Status&quot;).ToString() = &quot;PONOTRECEIVED&quot;]" DisplayName="ExtractFromDatalake - Copy_If_30" sap2010:WorkflowViewState.IdRef="If_5">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[failureCount]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[CInt(item("Failure_Count").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[lastRunTime]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item("Last_Run_Time").ToString()]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[fileExist]" sap2010:WorkflowViewState.IdRef="If_35">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_39">
                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_Append_Line_31" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[Environment.NewLine+&quot;Datalake Entry Available. Status : PONOTRECEIVED  Failure Count :&quot;+failureCount.tostring]" Source="[logFile]" />
                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_Append_Line_32" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="[&quot;Datalake Entry Values&quot;+Environment.NewLine  +&quot;Invoice Number : &quot; + DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString + Environment.NewLine + &quot;Purchase Order Number: &quot; + DictOcrValues(&quot;PO_NUMBER&quot;).ToString + Environment.NewLine  +&quot;Date: &quot; + DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString+ Environment.NewLine  + &quot;Sub Total: &quot; + DictOcrValues(&quot;SUBTOTAL&quot;).ToString+ Environment.NewLine + &quot;Tax: &quot; + DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString + Environment.NewLine  + &quot;Total: &quot; + DictOcrValues(&quot;TOTAL&quot;).ToString + Environment.NewLine +&quot;Shipping Handling charge : &quot;+ DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString+ Environment.NewLine +&quot;Discount : &quot;+ DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString]" Source="[logFile]" />
                                        <If Condition="[invoiceType = &quot;POINVOICE&quot;]" sap2010:WorkflowViewState.IdRef="If_49">
                                          <If.Then>
                                            <If Condition="[DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString.trim &lt;&gt; &quot;&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_48">
                                              <If.Then>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">DELIVERYNOTE</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </If.Then>
                                            </If>
                                          </If.Then>
                                        </If>
                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType}, {&quot;emailSubject&quot;,emailSubject},{&quot;vendorId&quot;,vendorId},{&quot;miscValues&quot;,miscValues},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;projectPath&quot;,projectPath},{&quot;configurationFolder&quot;,configurationFolder},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;failureCount&quot;,failureCount},{&quot;documentPath&quot;,configurationFolder + &quot;\Failure\&quot; + FileName},{&quot;includeDatalake&quot;,True},{&quot;invoiceType&quot;,invoiceType},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;division&quot;,division},{&quot;ListocrLineValues&quot;,ListocrLineValues}}]" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_33" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_16" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[processPoResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessPOInvoice.xaml&quot;]" />
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[CType(poInvoiceResponseDictionary("approvalList"), List(Of String))]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[processPoResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_34">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_38">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("status"), String)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item("RPA_Process_ID").ToString()]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[variationID]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item("Header_Variation_ID").ToString()]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[DLComments.EndsWith(CType(poInvoiceResponseDictionary(&quot;statusComments&quot;), String))]" sap2010:WorkflowViewState.IdRef="If_50">
                                                <If.Then>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[DLComments]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                                <If.Else>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments + "  " +CType(poInvoiceResponseDictionary("statusComments"), String)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Else>
                                              </If>
                                              <If Condition="[status = &quot;PONOTRECEIVED&quot;]" sap2010:WorkflowViewState.IdRef="If_33">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_36">
                                                    <If Condition="[failureCount + 1 &gt;= maxNotReceivedCount]" sap2010:WorkflowViewState.IdRef="If_47">
                                                      <If.Then>
                                                        <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;status&quot;,&quot;ALERT &quot; + status},{&quot;statusComments&quot;,&quot;ALERT!! Please receive the goods.&quot;},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;tenantID&quot;,tenantID},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;subTotal&quot;,DictOcrValues(&quot;SUBTOTAL&quot;).ToString},{&quot;total&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;drillbackLink&quot;,&quot;LogicalId=lid://infor.m3.m3&amp;program=APS450&amp;fieldNames=W2OBKV,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;,W3OBKV,,W1OBKV,&quot;+ division+&quot;&amp;includeStartPanel=True&amp;source=MForms&amp;requirePanel=True&amp;sortingOrder=2&amp;view=STD02-01&amp;tableName=FAPIBH&amp;keys=E5CONO,&quot;+miscValues(&quot;company&quot;).ToString+&quot;,E5DIVI,&quot;+division+&quot;,E5INBN,+&amp;parameters=XXSINO,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;&amp;startpanel=B&quot;},{&quot;invoiceDate&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString},{&quot;tax&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString},{&quot;charges&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;discount&quot;,DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString},{&quot;deliveryNote&quot;,DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString} ,{&quot;miscValues&quot;,miscValues},{&quot;APResp&quot;,APResp}}]" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_34" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_25" WorkflowFile="[projectPath+&quot;\SendNotification.xaml&quot;]" />
                                                      </If.Then>
                                                    </If>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("statusComments"), String)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,(failureCount+1).ToString},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_35" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_17" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_37">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments + "  " +CType(poInvoiceResponseDictionary("statusComments"), String)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCount.ToString},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_36" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_18" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                              <If Condition="[String.IsNullOrEmpty(APResp) or APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_52">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_55">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("APResp"), String)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp, variationID})
}]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_56">
                                                    <If Condition="[CType(poInvoiceResponseDictionary(&quot;APResp&quot;), String) &lt;&gt; &quot;&quot; AND NOT String.IsNullOrEmpty(CType(poInvoiceResponseDictionary(&quot;APResp&quot;), String)) AND APResp &lt;&gt; CType(poInvoiceResponseDictionary(&quot;APResp&quot;), String)]" sap2010:WorkflowViewState.IdRef="If_51">
                                                      <If.Then>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp, variationID})
}]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </If.Then>
                                                      <If.Else>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </If.Else>
                                                    </If>
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_37" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_19" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                            </If>
                            <If Condition="[item(&quot;Status&quot;).ToString() = &quot;MISSINGINFORMATION&quot; OR item(&quot;Status&quot;).ToString() = &quot;NEEDSVERIFICATION&quot;]" DisplayName="ExtractFromDatalake - Copy_If_38" sap2010:WorkflowViewState.IdRef="If_11">
                              <If.Then>
                                <If Condition="[fileExist]" sap2010:WorkflowViewState.IdRef="If_43">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                                      <If Condition="[item(&quot;Status&quot;).ToString() = &quot;NEEDSVERIFICATION&quot; AND invoiceType = &quot;EXPENSE&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="scg:List(x:String)" Name="linelistsasa" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String,Object))]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[item("GLCode").ToString()]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_36">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[accountingDim]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[item("Line_Data").ToString()]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(x:String)">[accountingDimArr]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[accountingDim.Split(New String() {Microsoft.VisualBasic.ControlChars.Lf}, StringSplitOptions.RemoveEmptyEntries).ToList]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ForEach x:TypeArguments="x:Object" DisplayName="ExtractFromDatalake - Copy_ForEach_39" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[accountingDimArr]">
                                                    <ActivityAction x:TypeArguments="x:Object">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="x:Object" Name="item" />
                                                      </ActivityAction.Argument>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[Dictline]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ForEach x:TypeArguments="x:String" DisplayName="ExtractFromDatalake - Copy_ForEach_40" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[item.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)]">
                                                          <ActivityAction x:TypeArguments="x:String">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[strKey]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[New String() {
    "SUPPLIER_ITEM_CODE",
    "DESCRIPTION",
    "QUANTITY",
    "UNIT_PRICE",
    "LINE_AMOUNT",
    "STATUS"
}(i)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Object">[Dictline(strKey)]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Object">[item2]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_10" MethodName="Add">
                                                          <InvokeMethod.TargetObject>
                                                            <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                          </InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[Dictline]</InArgument>
                                                        </InvokeMethod>
                                                      </Sequence>
                                                    </ActivityAction>
                                                  </ForEach>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                      </If>
                                      <If Condition="[invoiceType = &quot;POINVOICE&quot;]" sap2010:WorkflowViewState.IdRef="If_39">
                                        <If.Then>
                                          <If Condition="[DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString.trim &lt;&gt; &quot;&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_38">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">DELIVERYNOTE</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                        </If.Then>
                                      </If>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item("RPA_Process_ID").ToString()]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[variationID]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item("Header_Variation_ID").ToString()]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_Append_Line_41" sap2010:WorkflowViewState.IdRef="Append_Line_15" Line="[Environment.NewLine+&quot;Datalake Entry Available. Status : &quot;+item(&quot;Status&quot;).ToString()+&quot; Is updated by user : Y&quot;]" Source="[logFile]" />
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_Append_Line_42" sap2010:WorkflowViewState.IdRef="Append_Line_16" Line="[&quot;Datalake Entry Values&quot;+Environment.NewLine  +&quot;Invoice Number : &quot; + DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString + Environment.NewLine + &quot;Purchase Order Number: &quot; + DictOcrValues(&quot;PO_NUMBER&quot;).ToString + Environment.NewLine  +&quot;Date: &quot; + DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString+ Environment.NewLine  + &quot;Sub Total: &quot; + DictOcrValues(&quot;SUBTOTAL&quot;).ToString+ Environment.NewLine + &quot;Tax: &quot; + DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString + Environment.NewLine  + &quot;Total: &quot; + DictOcrValues(&quot;TOTAL&quot;).ToString + Environment.NewLine +&quot;Shipping Handling charge : &quot;+ DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString+ Environment.NewLine +&quot;Discount : &quot;+ DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString]" Source="[logFile]" />
                                      <If Condition="[DictOcrValues(&quot;PO_NUMBER&quot;).ToString.trim = &quot;&quot; AND DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString.trim = &quot;&quot; AND (invoiceType = &quot;DELIVERYNOTE&quot; OR invoiceType = &quot;POINVOICE&quot;)]" sap2010:WorkflowViewState.IdRef="If_42">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments + "   "+"Enter either PO or Delivery note number if it is a PO INVOICE"]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_Append_Line_43" sap2010:WorkflowViewState.IdRef="Append_Line_17" Line="[statusComments]" Source="[logFile]" />
                                            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,Status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCountStr},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_44" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_20" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_45" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_21" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;vendorNames&quot;,DictOcrValues(&quot;VENDOR_NAME&quot;).ToString},{&quot;logFile&quot;,logFile},{&quot;invoiceType&quot;,invoiceType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;miscValues&quot;,miscValues},{&quot;distributionType&quot;,distributionType}, {&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;projectPath&quot;,projectPath}, {&quot;manualEntry&quot;,false},{&quot;configurationFolder&quot;,configurationFolder},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;failureCount&quot;,0},{&quot;documentPath&quot;,configurationFolder + &quot;\Failure\&quot; + FileName},{&quot;includeDatalake&quot;,True},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;GLCode&quot;,GLCode},{&quot;division&quot;,division},{&quot;vendorId&quot;,vendorId},{&quot;ListocrLineValues&quot;,ListocrLineValues}}]" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_46" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_22" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[processPoResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessPOInvoice.xaml&quot;]" />
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:List(x:String)">[CType(poInvoiceResponseDictionary("approvalList"), List(Of String))]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[processPoResponseCode=200]" sap2010:WorkflowViewState.IdRef="If_41">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("status"), String)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[status = &quot;PONOTRECEIVED&quot;]" sap2010:WorkflowViewState.IdRef="If_40">
                                                    <If.Then>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[failureCountStr]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">1</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </If.Then>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments +"  " +CType(poInvoiceResponseDictionary("statusComments"), String)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,Status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCountStr},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_47" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_23" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                  <If Condition="[String.IsNullOrEmpty(APResp) or APResp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_56">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_59">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("APResp"), String)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp, variationID})
}]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_60">
                                                        <If Condition="[CType(poInvoiceResponseDictionary(&quot;APResp&quot;), String) &lt;&gt; &quot;&quot; AND NOT String.IsNullOrEmpty(CType(poInvoiceResponseDictionary(&quot;APResp&quot;), String)) AND APResp &lt;&gt; CType(poInvoiceResponseDictionary(&quot;APResp&quot;), String)]" sap2010:WorkflowViewState.IdRef="If_55">
                                                          <If.Then>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_174">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp, variationID})
}]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_48" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_24" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                            <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_25">
                              <If.Then>
                                <If Condition="[approvalList.count&gt;0]" sap2010:WorkflowViewState.IdRef="If_44">
                                  <If.Then>
                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_11" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                    </InvokeMethod>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_27">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
          <If Condition="[approvalLists.count&gt;0]" sap2010:WorkflowViewState.IdRef="If_26">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;approvalLists&quot;,approvalLists},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="ExtractFromDatalake - Copy_InvokeWorkflow_49" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_15" WorkflowFile="[projectPath+&quot;\approval.xaml&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="2052.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="2052.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="2052.66666666667,158">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="2052.66666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="2052.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="1904.66666666667,62" />
      <sap2010:ViewStateData Id="Delay_1" sap:VirtualizedContainerService.HintSize="1904.66666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="1904.66666666667,22" />
      <sap2010:ViewStateData Id="Delay_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="1756.66666666667,372" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1756.66666666667,300" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="1608.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="1556,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_147" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_148" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_150" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_149" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_2" sap:VirtualizedContainerService.HintSize="464,554" />
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="486,882">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_144" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_145" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="1556,1036" />
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_22" sap:VirtualizedContainerService.HintSize="1556,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="1556,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="1556,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="636,370" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="1556,466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_154" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="1556,62" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="650,62" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="650,62" />
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="650,62" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="471.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="471.333333333333,62" />
      <sap2010:ViewStateData Id="CommentOut_7" sap:VirtualizedContainerService.HintSize="418.666666666667,66" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="418.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404.666666666667,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="418.666666666667,514.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_5" sap:VirtualizedContainerService.HintSize="418.666666666667,66" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="418.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="440.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="471.333333333333,1312.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="471.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="493.333333333333,1814.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="524,1967.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="650,2121.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="1556,2551.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="286,359.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_6" sap:VirtualizedContainerService.HintSize="1556,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="1556,22" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="1118,62" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="1118,62" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="970,22" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="970,22" />
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="970,365.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_16" sap:VirtualizedContainerService.HintSize="970,22" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="970,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="822,213.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="464,212" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_17" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="486,499.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="822,651.333333333333" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,290.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="510,217.333333333333" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="532,341.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="822,493.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_19" sap:VirtualizedContainerService.HintSize="822,22" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="844,1928">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="970,2080" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="992,2896.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="1118,3048.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="1140,3376.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="1556,3530.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="495.333333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="495.333333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="347.333333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="347.333333333333,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="294.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="294.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="264,392">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="294.666666666667,543.333333333333" />
      <sap2010:ViewStateData Id="InvokeMethod_10" sap:VirtualizedContainerService.HintSize="294.666666666667,132" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="316.666666666667,1042">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="347.333333333333,1193.33333333333" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="369.333333333333,1520">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="495.333333333333,1672" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="517.333333333333,1998.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="1282,2150.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="1282,365.333333333333" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="1282,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="1282,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_15" sap:VirtualizedContainerService.HintSize="1282,22" />
      <sap2010:ViewStateData Id="Append_Line_16" sap:VirtualizedContainerService.HintSize="1282,22" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_17" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_20" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_21" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,578">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_22" sap:VirtualizedContainerService.HintSize="970,22" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="970,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="822,213.333333333333" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="822,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_23" sap:VirtualizedContainerService.HintSize="822,22" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="264,290.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_174" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="510,217.333333333333" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="532,341.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="822,493.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_24" sap:VirtualizedContainerService.HintSize="822,22" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="844,1197.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="970,1349.33333333333" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="992,1636.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="1282,1788.66666666667" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="1304,4835.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="1430,4987.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1556,5139.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_11" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,284" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="1556,436" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="1578,17474.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="1608.66666666667,17627.3333333333" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1630.66666666667,17853.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1756.66666666667,18007.3333333333" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1778.66666666667,18883.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="1904.66666666667,19037.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1926.66666666667,19387.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="2052.66666666667,19541.3333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_15" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="464,212" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="486,336">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="2052.66666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="2074.66666666667,20323.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="2114.66666666667,20443.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldExpandAll">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
