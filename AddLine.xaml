﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="AddLine_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
      <Variable x:TypeArguments="njl:JToken" Name="out7" />
      <Variable x:TypeArguments="x:String" Name="itno" />
      <Variable x:TypeArguments="x:Decimal" Name="totAmt" />
      <Variable x:TypeArguments="x:Decimal" Name="totQty" />
      <Variable x:TypeArguments="x:String" Name="diffAmt" />
      <Variable x:TypeArguments="x:String" Name="ceid" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="x:String" Name="cdse" />
    </Sequence.Variables>
    <ForEach x:TypeArguments="s:String[]" DisplayName="AddLine_ForEach_2" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[M3TotalTableRows]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
        </ActivityAction.Argument>
        <Sequence DisplayName="AddLine_Sequence_3" sap2010:WorkflowViewState.IdRef="Sequence_7">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="inbn" />
            <Variable x:TypeArguments="x:String" Name="puno" />
            <Variable x:TypeArguments="x:String" Name="pnli" />
            <Variable x:TypeArguments="x:String" Name="repn" />
            <Variable x:TypeArguments="x:String" Name="ivqa" />
            <Variable x:TypeArguments="x:String" Name="grpr" />
            <Variable x:TypeArguments="x:String" Name="pnli2" />
            <Variable x:TypeArguments="x:String" Name="pnls" />
          </Sequence.Variables>
          <Assign DisplayName="AddLine_Assign_inbn_4" sap2010:WorkflowViewState.IdRef="Assign_89">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[inbn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[inbnValue]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddLine_Assign_puno_5" sap2010:WorkflowViewState.IdRef="Assign_90">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddLine_Assign_pnli_6" sap2010:WorkflowViewState.IdRef="Assign_91">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddLine_Assign_itno_7" sap2010:WorkflowViewState.IdRef="Assign_92">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddLine_Assign_repn_8" sap2010:WorkflowViewState.IdRef="Assign_93">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddLine_Assign_ivqa_9" sap2010:WorkflowViewState.IdRef="Assign_94">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(10)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnls]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(11)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="AddLine_Assign_grpr_10" sap2010:WorkflowViewState.IdRef="Assign_95">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[Math.Round(convert.ToDecimal(rows(2)),4).ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnli2]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[pnli.substring(0,pnli.length-2)]</InArgument>
            </Assign.Value>
          </Assign>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddLine_IONAPIRequestWizard_11" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj7]" StatusCode="[StatusCode7]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/GetLine?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>PNLI</x:String>
                  <x:String>PUNO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>pnli2</x:String>
                  <x:String>puno</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <Assign DisplayName="AddLine_Assign_out7_12" sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out7]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj7.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out7(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="AddLine_If_13" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Sequence DisplayName="AddLine_Sequence_14" sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign DisplayName="AddLine_Assign_commentStatus_15" sap2010:WorkflowViewState.IdRef="Assign_2">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Purchase price UOM and purchase order UOM are not available."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddLine_Assign_Status_16" sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_17" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="AddLine_Sequence_18" sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="out8" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
                  <Variable x:TypeArguments="x:Int32" Name="m" />
                  <Variable x:TypeArguments="x:String" Name="ppun" />
                  <Variable x:TypeArguments="x:String" Name="puun" />
                </Sequence.Variables>
                <Assign DisplayName="AddLine_Assign_ppun_19" sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ppun]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PPUN").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddLine_Assign_puun_20" sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[puun]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PUUN").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="AddLine_Assign_puno_21" sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[m=0 AND vatCodeConfig = &quot;LINE&quot;]" DisplayName="AddLine_If_22" sap2010:WorkflowViewState.IdRef="If_3">
                  <If.Then>
                    <Sequence DisplayName="AddLine_Sequence_23" sap2010:WorkflowViewState.IdRef="Sequence_3">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="iru:ResponseObject" Name="vatResp" />
                        <Variable x:TypeArguments="njl:JToken" Name="vatOut" />
                        <Variable x:TypeArguments="x:String" Name="vatCode" />
                      </Sequence.Variables>
                      <If Condition="[Out7(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;&quot; AND Out7(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;0&quot;]" DisplayName="AddLine_If_24" sap2010:WorkflowViewState.IdRef="If_1">
                        <If.Then>
                          <Assign DisplayName="AddLine_Assign_vatCode_25" sap2010:WorkflowViewState.IdRef="Assign_8">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Out7("results")(0)("records")(0)("VTCD").ToString]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
                <Assign DisplayName="AddLine_Assign_m_26" sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[m]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">[m+1]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[pnli.endswith(&quot;00&quot;)]" sap2010:WorkflowViewState.IdRef="If_7">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[pnli.substring(0,pnli.length-2)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Sequence DisplayName="AddLine_Sequence_27" sap2010:WorkflowViewState.IdRef="Sequence_14">
                        <Assign DisplayName="AddLine_Assign_ceid_28" sap2010:WorkflowViewState.IdRef="Assign_100">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">E20022</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="AddLine_Assign_req2_29" sap2010:WorkflowViewState.IdRef="Assign_101">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["F3CDSE from FGRPCL where F3CEID = '" + ceid+"'"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddLine_IONAPIRequestWizard_30" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>QERY</x:String>
                                <x:String>SEPC</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>req2</x:String>
                                <x:String>~</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <If Condition="[StatusCode2 = 200]" DisplayName="AddLine_If_31" sap2010:WorkflowViewState.IdRef="If_11">
                          <If.Then>
                            <Sequence DisplayName="AddLine_Sequence_32" sap2010:WorkflowViewState.IdRef="Sequence_12">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="njl:JToken" Name="out2" />
                              </Sequence.Variables>
                              <Assign DisplayName="AddLine_Assign_out2_33" sap2010:WorkflowViewState.IdRef="Assign_102">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" DisplayName="AddLine_If_34" sap2010:WorkflowViewState.IdRef="If_9">
                                <If.Then>
                                  <Sequence DisplayName="AddLine_Sequence_35" sap2010:WorkflowViewState.IdRef="Sequence_10">
                                    <Assign DisplayName="AddLine_Assign_cdse_36" sap2010:WorkflowViewState.IdRef="Assign_103">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_37" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                </If.Else>
                              </If>
                              <If Condition="[cdse &lt;&gt; &quot;&quot;]" DisplayName="AddLine_If_38" sap2010:WorkflowViewState.IdRef="If_10">
                                <If.Then>
                                  <Sequence DisplayName="AddLine_Sequence_39" sap2010:WorkflowViewState.IdRef="Sequence_11">
                                    <Assign DisplayName="AddLine_Assign_cdse_40" sap2010:WorkflowViewState.IdRef="Assign_104">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddLine_IONAPIRequestWizard_41" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                      <iai:IONAPIRequestWizard.Headers>
                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>Accept</x:String>
                                          </scg:List>
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>application/json</x:String>
                                          </scg:List>
                                        </scg:List>
                                      </iai:IONAPIRequestWizard.Headers>
                                      <iai:IONAPIRequestWizard.QueryParameters>
                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                            <x:String>INBN</x:String>
                                            <x:String>RDTP</x:String>
                                            <x:String>DIVI</x:String>
                                            <x:String>CDSE</x:String>
                                            <x:String>CEID</x:String>
                                            <x:String>ITNO</x:String>
                                            <x:String>PNLI</x:String>
                                            <x:String>PUNO</x:String>
                                            <x:String>REPN</x:String>
                                            <x:String>IVQA</x:String>
                                            <x:String>GRPR</x:String>
                                          </scg:List>
                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                            <x:String>inbnValue</x:String>
                                            <x:String>5</x:String>
                                            <x:String>division</x:String>
                                            <x:String>cdse</x:String>
                                            <x:String>ceid</x:String>
                                            <x:String>itno</x:String>
                                            <x:String>pnli</x:String>
                                            <x:String>puno</x:String>
                                            <x:String>repn</x:String>
                                            <x:String>ivqa</x:String>
                                            <x:String>grpr</x:String>
                                          </scg:List>
                                        </scg:List>
                                      </iai:IONAPIRequestWizard.QueryParameters>
                                    </iai:IONAPIRequestWizard>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_42" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="Line charge added" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_43" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence DisplayName="AddLine_Sequence_44" sap2010:WorkflowViewState.IdRef="Sequence_13">
                              <Assign DisplayName="AddLine_Assign_commentStatus_45" sap2010:WorkflowViewState.IdRef="Assign_106">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="AddLine_Assign_Status_46" sap2010:WorkflowViewState.IdRef="Assign_107">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_47" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[commentStatus]" Source="[logfile]" />
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                      <iad:CommentOut DisplayName="AddLine_CommentOut_48" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                        <iad:CommentOut.Activities>
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddLine_IONAPIRequestWizard_49" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                  <x:String>INBN</x:String>
                                  <x:String>RDTP</x:String>
                                  <x:String>DIVI</x:String>
                                  <x:String>GRPR</x:String>
                                  <x:String>ITNO</x:String>
                                  <x:String>IVQA</x:String>
                                  <x:String>PNLI</x:String>
                                  <x:String>PUNO</x:String>
                                  <x:String>RELP</x:String>
                                  <x:String>REPN</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                  <x:String>inbn</x:String>
                                  <x:String>5</x:String>
                                  <x:String>division</x:String>
                                  <x:String>grpr</x:String>
                                  <x:String>itno</x:String>
                                  <x:String>ivqa</x:String>
                                  <x:String>pnli</x:String>
                                  <x:String>puno</x:String>
                                  <x:String>1</x:String>
                                  <x:String>repn</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </iad:CommentOut.Activities>
                      </iad:CommentOut>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[pnli.substring(0,pnli.length-2)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddLine_IONAPIRequestWizard_50" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="16">
                              <x:String>INBN</x:String>
                              <x:String>RDTP</x:String>
                              <x:String>DIVI</x:String>
                              <x:String>GRPR</x:String>
                              <x:String>ITNO</x:String>
                              <x:String>IVQA</x:String>
                              <x:String>PNLI</x:String>
                              <x:String>PUNO</x:String>
                              <x:String>RELP</x:String>
                              <x:String>REPN</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="16">
                              <x:String>inbn</x:String>
                              <x:String>1</x:String>
                              <x:String>division</x:String>
                              <x:String>grpr</x:String>
                              <x:String>itno</x:String>
                              <x:String>ivqa</x:String>
                              <x:String>pnli</x:String>
                              <x:String>puno</x:String>
                              <x:String>1</x:String>
                              <x:String>repn</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                    </Sequence>
                  </If.Else>
                </If>
                <Assign DisplayName="AddLine_Assign_out8_51" sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="AddLine_If_52" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <Sequence DisplayName="AddLine_Sequence_53" sap2010:WorkflowViewState.IdRef="Sequence_8">
                      <Assign DisplayName="AddLine_Assign_commentStatus_54" sap2010:WorkflowViewState.IdRef="Assign_96">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_55" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="AddLine_Sequence_56" sap2010:WorkflowViewState.IdRef="Sequence_4">
                      <Assign DisplayName="AddLine_Assign_commentStatus_57" sap2010:WorkflowViewState.IdRef="Assign_11">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">Invoice Line has been created</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="AddLine_Append_Line_58" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="1444,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1444,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="1133,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1133,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="1133,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1133,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1133,60" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="822,60" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="800,60" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="800,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="800,22" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="489,60" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="489,332" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,308">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="489,456" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="511,1052">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="800,1200">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="822,1586">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="822,118" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="844,1968">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1133,2116" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="1133,60" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1133,394" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1155,3265">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="1444,3413" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="1466,4599">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="1496,4747">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1518,4871">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1558,5031" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
