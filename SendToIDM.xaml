﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.poNumber="1234po" this:Workflow.invoiceNumber="1234Inv" this:Workflow.companyNumber="999" this:Workflow.supplierNo="12345sino" this:Workflow.documentType="M3_SupplierInvoice" this:Workflow.documentPath="C:\Users\<USER>\Downloads\3_mm_100703_no variance.pdf" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_DEV/" this:Workflow.division="670" this:Workflow.correlationID="xxxxx" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:this="clr-namespace:RehostedWorkflowDesigner" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="poNumber" Type="InArgument(x:String)" />
    <x:Property Name="invoiceNumber" Type="InArgument(x:String)" />
    <x:Property Name="responseMessage" Type="OutArgument(x:String)" />
    <x:Property Name="companyNumber" Type="InArgument(x:String)" />
    <x:Property Name="supplierNo" Type="InArgument(x:String)" />
    <x:Property Name="documentType" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="InArgument(x:String)" />
    <x:Property Name="AYear" Type="InArgument(x:String)" />
    <x:Property Name="InYear" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SendToIDM_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="base64String" />
      <Variable x:TypeArguments="x:String" Name="fileName" />
      <Variable x:TypeArguments="x:String" Name="idmRequestString" />
      <Variable x:TypeArguments="njl:JToken" Name="idmRequestJtoken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="idmResponse" />
      <Variable x:TypeArguments="x:Int32" Name="idmResponseStatus" />
      <Variable x:TypeArguments="x:String" Name="compDiv" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
    </Sequence.Variables>
    <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64String]" ContinueOnError="True" DisplayName="SendToIDM_FileToBase64_2" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_1" />
    <Assign DisplayName="SendToIDM_Assign_fileName_3" sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[(documentPath.Substring(documentPath.LastIndexOf("\"c)+1,(documentPath.Length()-documentPath.LastIndexOf("\"c))-1)).Replace("'","").Replace("""","")]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SendToIDM_Assign_compDiv_4" sap2010:WorkflowViewState.IdRef="Assign_7">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[compDiv]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[companyNumber+"_"+division]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SendToIDM_Assign_invoiceNumber_5" sap2010:WorkflowViewState.IdRef="Assign_8">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[invoiceNumber]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[invoiceNumber.ToUpper]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToIDM_Template_Apply_6" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'item': { 'attrs': { 'attr': [ { 'name': 'BOD_SupplierInvoiceID', 'value': '{{%invoiceNumber%}}' }, { 'name': 'M3_Company', 'value': '{{%companyNumber%}}' },{ 'name': 'BOD_SupplierPartyID', 'value': '{{%supplierNo%}}' },{ 'name': 'PurchaseOrder', 'value': '{{%poNumber%}}' },{ 'name': 'BOD_AlternateDocumentID_1', 'value': '{{%correlationID%}}' },{ 'name': 'M3_Division', 'value': '{{%division%}}' },{ 'name': 'BOD_AccountingEntityID', 'value': '{{%compDiv%}}' } ,{ 'name': 'M3_InvoiceYear', 'value': '{{%InYear%}}' },{ 'name': 'M3_AccountingYear', 'value': '{{%AYear%}}' }  ] }, 'resrs': { 'res': [ { 'filename': '{{%item%}}', 'base64':'{{%base64String%}}' } ] }, 'acl': { 'name': 'Public' }, 'entityName': '{{%documentType%}}' } }" Text="[idmRequestString]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="16">
            <x:String>invoiceNumber</x:String>
            <x:String>companyNumber</x:String>
            <x:String>base64String</x:String>
            <x:String>item</x:String>
            <x:String>supplierNo</x:String>
            <x:String>poNumber</x:String>
            <x:String>documentType</x:String>
            <x:String>correlationID</x:String>
            <x:String>division</x:String>
            <x:String>compDiv</x:String>
            <x:String>AYear</x:String>
            <x:String>InYear</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="16">
            <x:String>invoiceNumber</x:String>
            <x:String>companyNumber</x:String>
            <x:String>base64String</x:String>
            <x:String>fileName</x:String>
            <x:String>supplierNo</x:String>
            <x:String>poNumber</x:String>
            <x:String>documentType</x:String>
            <x:String>correlationID</x:String>
            <x:String>division</x:String>
            <x:String>compDiv</x:String>
            <x:String>AYear</x:String>
            <x:String>InYear</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToIDM_DeserializeJSON_7" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[idmRequestJtoken]" JTokenString="[idmRequestString]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SendToIDM_IONAPIRequestWizard_8" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[idmRequestJtoken.tostring]" Response="[idmResponse]" StatusCode="[idmResponseStatus]" Url="[tenantID+ &quot;IDM/api/items&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>$Language</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>en-US</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[idmResponseStatus= 200]" DisplayName="SendToIDM_If_9" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <If Condition="[idmResponse.readastext.Contains(&quot;item&quot;)]" DisplayName="SendToIDM_If_10" sap2010:WorkflowViewState.IdRef="If_1">
          <If.Then>
            <Assign DisplayName="SendToIDM_Assign_responseMessage_11" sap2010:WorkflowViewState.IdRef="Assign_2">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">Document added to IDM</InArgument>
              </Assign.Value>
            </Assign>
          </If.Then>
          <If.Else>
            <Assign DisplayName="SendToIDM_Assign_responseMessage_12" sap2010:WorkflowViewState.IdRef="Assign_3">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">["Failure while saving Document to IDM"]</InArgument>
              </Assign.Value>
            </Assign>
          </If.Else>
        </If>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="SendToIDM_Sequence_13" sap2010:WorkflowViewState.IdRef="Sequence_3">
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToIDM_Template_Apply_14" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{ 'item': { 'attrs': { 'attr': [ { 'name': 'BOD_SupplierInvoiceID', 'value': '{{%invoiceNumber%}}' }, { 'name': 'M3_Company', 'value': '{{%companyNumber%}}' },{ 'name': 'BOD_SupplierPartyID', 'value': '{{%supplierNo%}}' },{ 'name': 'M3_PurchaseOrderNumber', 'value': '{{%poNumber%}}' },{ 'name': 'BOD_AlternateDocumentID_1', 'value': '{{%correlationID%}}' },{ 'name': 'M3_Division', 'value': '{{%division%}}' } ] }, 'resrs': { 'res': [ { 'filename': '{{%item%}}', 'base64':'{{%base64String%}}' } ] }, 'acl': { 'name': 'Public' }, 'entityName': '{{%documentType%}}' } }" Text="[idmRequestString]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="16">
                  <x:String>invoiceNumber</x:String>
                  <x:String>companyNumber</x:String>
                  <x:String>base64String</x:String>
                  <x:String>item</x:String>
                  <x:String>supplierNo</x:String>
                  <x:String>poNumber</x:String>
                  <x:String>documentType</x:String>
                  <x:String>correlationID</x:String>
                  <x:String>division</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="16">
                  <x:String>invoiceNumber</x:String>
                  <x:String>companyNumber</x:String>
                  <x:String>base64String</x:String>
                  <x:String>fileName</x:String>
                  <x:String>supplierNo</x:String>
                  <x:String>poNumber</x:String>
                  <x:String>documentType</x:String>
                  <x:String>correlationID</x:String>
                  <x:String>division</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToIDM_DeserializeJSON_15" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[idmRequestJtoken]" JTokenString="[idmRequestString]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SendToIDM_IONAPIRequestWizard_16" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[idmRequestJtoken.tostring]" Response="[idmResponse]" StatusCode="[idmResponseStatus]" Url="[tenantID+ &quot;IDM/api/items&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>$Language</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>en-US</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[idmResponseStatus= 200]" DisplayName="SendToIDM_If_17" sap2010:WorkflowViewState.IdRef="If_4">
            <If.Then>
              <If Condition="[idmResponse.readastext.Contains(&quot;item&quot;)]" DisplayName="SendToIDM_If_18" sap2010:WorkflowViewState.IdRef="If_3">
                <If.Then>
                  <Assign DisplayName="SendToIDM_Assign_responseMessage_19" sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">Document added to IDM</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Then>
                <If.Else>
                  <Assign DisplayName="SendToIDM_Assign_responseMessage_20" sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">["Failure while saving Document to IDM"]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Else>
              </If>
            </If.Then>
            <If.Else>
              <Assign DisplayName="SendToIDM_Assign_responseMessage_21" sap2010:WorkflowViewState.IdRef="Assign_4">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[responseMessage]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">["Failure while saving Document to IDM"]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Else>
          </If>
        </Sequence>
      </If.Else>
    </If>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendToIDM_Append_Line_22" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[responseMessage]" Source="[logFile]" />
    <sads:DebugSymbol.Symbol>d1VDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFNlbmRUb0lETS54YW1sRwHYAQHiAQEKAZMBAZsBAQkB1QMB2QMBCAG5AQG9AQEHAfcDAf0DAQYBrwIB6gIBBQH/AQGSAgEEAYMDAbwDAQMBbgF1AQJOA6ICDgIBAVkFWdgBAgFbWgVhDgIBU2IFaQ4CAU1qBXEOAgFIcgWTARoCAUSUAQWUAd8BAgE/lQEFqgEfAgE3qwEFnwIKAgEHoAIFoALQAQIBAlkzWUMCAV5ZlAFZpAECAVxfMF/GAQIBVlwxXDsCAVRnMGdMAgFQZDFkOgIBTm8wb0cCAUtsMWxAAgFJcpgHcqwHAgFGcrABcpIHAgFFlAHIAZQB3AECAUKUAaYBlAG6AQIBQJUBjAKVAakCAgE+lQGzApUBwgICATyVAegClQGPAwIBOpUBzgKVAeMCAgE4qwETqwEtAgEIrQEJwgEOAgEtxQEJnQIUAgEKoAKnAaACugECAQWgAsIBoALNAQIBA60BF60BTAIBLq8BDbYBFgIBM7kBDcABFgIBL8YBC+EBIAIBKeIBC+IB5QECASTjAQv4ASUCARz5AQucAhACAQu0ATi0AU0CATaxATmxAUoCATS+ATi+AWACATK7ATm7AUoCATDGAfQFxgGIBgIBK8YBsgHGAe4FAgEq4gHOAeIB4gECASfiAawB4gHAAQIBJeMBkgLjAa8CAgEj4wG5AuMByAICASHjAe4C4wGVAwIBH+MB1ALjAekCAgEd+QEZ+QEzAgEM+wEPkAIUAgESkwIPmgIYAgEO+wEd+wFSAgET/QEThAIcAgEYhwITjgIcAgEUmAI6mAJiAgERlQI7lQJMAgEPggI+ggJTAgEb/wE//wFQAgEZjAI+jAJmAgEXiQI/iQJQAgEV</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="FileToBase64_1" sap:VirtualizedContainerService.HintSize="1332,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1332,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="1332,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="1332,60" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="1332,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="1332,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="1332,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="776,356" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="798,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="1332,814">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="1332,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1354,1548">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1394,1628" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
