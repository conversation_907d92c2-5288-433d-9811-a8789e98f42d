﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.tenantId="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/" this:Workflow.vendorId="20FGCASE" this:Workflow.logfile="C:\M3VendorInvoiceProcessing\Logs\test.txt" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:this="clr-namespace:RehostedWorkflowDesigner" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantId" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="result" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="getvendordetails_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_10">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode3" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj3" />
      <Variable x:TypeArguments="njl:JToken" Name="out3" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
      <Variable x:TypeArguments="njl:JToken" Name="out4" />
      <Variable x:TypeArguments="x:String" Name="authUser" />
      <Variable x:TypeArguments="x:String" Name="vendorName" />
      <Variable x:TypeArguments="x:String" Name="bkid" />
      <Variable x:TypeArguments="x:String" Name="SupAcho" />
      <Variable x:TypeArguments="x:String" Name="countryCode" />
      <Variable x:TypeArguments="x:String" Name="discountTerms" />
      <Variable x:TypeArguments="x:String" Name="txap" />
      <Variable x:TypeArguments="x:String" Name="Currency" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[result]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[discountTerms]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="getvendordetails_IONAPIRequestWizard_2" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj3]" StatusCode="[StatusCode3]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>extendedresult</x:String>
            <x:String>format</x:String>
            <x:String>righttrim</x:String>
            <x:String>excludeempty</x:String>
            <x:String>dateformat</x:String>
            <x:String>SUNO</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>false</x:String>
            <x:String>PRETTY</x:String>
            <x:String>true</x:String>
            <x:String>false</x:String>
            <x:String>YMD8</x:String>
            <x:String>vendorId</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode3 = 200]" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj3.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[countryCode]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("CSCD").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[discountTerms]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("TECD").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("RESP").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[txap]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("TXAP").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Currency]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("CUCD").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Vendor name is not obtained for the Supplier Number " + vendorId+"."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getvendordetails_Append_Line_3" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getvendordetails_Append_Line_4" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <Assign DisplayName="getvendordetails_Assign_SupAcho_5" sap2010:WorkflowViewState.IdRef="Assign_14">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["ACHO:"+vendorId + " AND BKTP: 03"]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="getvendordetails_IONAPIRequestWizard_6" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>SQRY</x:String>
            <x:String>dateformat</x:String>
            <x:String>excludeempty</x:String>
            <x:String>righttrim</x:String>
            <x:String>format</x:String>
            <x:String>extendedresult</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>SupAcho</x:String>
            <x:String>YMD8</x:String>
            <x:String>false</x:String>
            <x:String>true</x:String>
            <x:String>PRETTY</x:String>
            <x:String>false</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_5">
      <If.Then>
        <Sequence DisplayName="getvendordetails_Sequence_7" sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                <Assign DisplayName="getvendordetails_Assign_bkid_8" sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Assign DisplayName="getvendordetails_Assign_bkid_9" sap2010:WorkflowViewState.IdRef="Assign_11">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="commen" />
            <Variable x:TypeArguments="x:String" Name="SupplierNo" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Banking details for the Supplier Number " + SupplierNo +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="getvendordetails_Append_Line_10" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[VendorName]</InArgument>
    </InvokeMethod>
    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[authUser]</InArgument>
    </InvokeMethod>
    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[bkid]</InArgument>
    </InvokeMethod>
    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[countryCode]</InArgument>
    </InvokeMethod>
    <InvokeMethod DisplayName="getvendordetails_InvokeMethod_11" sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[discountTerms]</InArgument>
    </InvokeMethod>
    <InvokeMethod DisplayName="getvendordetails_InvokeMethod_12" sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[txap]</InArgument>
    </InvokeMethod>
    <InvokeMethod DisplayName="getvendordetails_InvokeMethod_13" sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
      <InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
      </InvokeMethod.TargetObject>
      <InArgument x:TypeArguments="x:String">[Currency]</InArgument>
    </InvokeMethod>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXGdldHZlbmRvcmRldGFpbHMueGFtbHMBbgGlAQEEAb4BAccBAQMB3wEBigIBAlwDnAMOAgEBbQV0DgMBoAF1BX4OAwGcAX8FngEfAwGVAZ8BBYwCCgIBVI0CBZQCDgIBT5UCBaoCHwIBSKsCBfACCgIBJfECBfYCFAIBIPcCBfwCFAIBG/0CBYIDFAIBFoMDBYgDFAIBEYkDBY4DFAIBDI8DBZQDFAIBB5UDBZoDFAIBAnI6clEDAaMBbztvQwMBoQF7C3s6AwGfAXcxd0ADAZ0Bf5oCf6YCAwGaAX/GAn+PAwMBmAF/sgJ/wQIDAZYBnwETnwEoAgFVoQEJ9QEUAgFm+AEJigIUAgFXkgIwkgJUAgFSjwIxjwI6AgFQlQKmApUCsgICAU2VAtIClQKbAwIBS5UCvgKVAs0CAgFJqwITqwIoAgEmrQIJ1QIUAgE32AIJ7gIUAgEo8wI68wJCAgEj9QIu9QI6AgEh+QI6+QJCAgEe+wIu+wI4AgEc/wI6/wJCAgEZgQMugQM0AgEXhQM6hQNCAgEUhwMuhwM7AgESiwM6iwNCAgEPjQMujQM9AgENkQM6kQNCAgEKkwMukwM0AgEIlwM6lwNCAgEFmQMumQM4AgEDogELqQEUAwGRAaoBC/QBEAIBZ/kBC4ACFAIBYYECC4gCFAIBXYkCC4kCvgECAVixAgu4AhQCAUS5AgvUAhACATjdAgvkAhQCATLlAgvsAhQCAS7tAgvtAr4BAgEppwE4pwFbAwGUAaQBOaQBPwMBkgGqARmqAW8CAWisAQ/dARoCAXjgAQ/yARoCAWn+ATb+AY8BAgFk+wE3+wFGAgFihgI2hgJBAgFggwI3gwI/AgFeiQKXAYkCqAECAVuJArABiQK7AQIBWbYCOLYCWwIBR7MCObMCPwIBRbkCGbkCbwIBObsCD8QCGgIBP8cCD9ICGgIBOuICNuICnAECATXfAjffAkYCATPqAjbqAkECATHnAjfnAj8CAS/tApcB7QKoAQIBLO0CsAHtArsBAgEqrQERtAEaAwGNAbUBEbwBGgMBiQG9ARHEARoDAYUBxQERzAEaAwGBAc0BEdQBGgIBfdUBEdwBGgIBeeEBEegBGgIBc+kBEfABGgIBb/EBEfEBxAECAWq8AhHDAhoCAUDIAhHRAhoCATuyATyyAW8DAZABrwE9rwFJAwGOAboBPLoBbwMBjAG3AT23AUoDAYoBwgE8wgFvAwGIAb8BPb8BTAMBhgHKATzKAW8DAYQBxwE9xwFHAwGCAdIBPNIBbwMBgAHPAT3PAUMCAX7aATzaAW8CAXzXAT3XAUcCAXrmATzmAYMBAgF24wE94wFMAgF07gE87gFHAgFy6wE96wFFAgFw8QGdAfEBrgECAW3xAbYB8QHBAQIBa8ECPMECcQIBQ74CPb4CQwIBQc4CF84CRgIBPsoCPcoCQwIBPA==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="864,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,684">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="553,832" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="575,1056">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="864,1204" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="864,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="553,332" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="575,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="864,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="864,128" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="886,3019">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="926,3139" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
