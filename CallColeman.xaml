﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="APIString" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="comments" Type="OutArgument(x:String)" />
    <x:Property Name="httpOut" Type="OutArgument(njl:JToken)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="CallColeman_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_7">
    <Sequence DisplayName="CallColeman_Sequence_2" sap2010:WorkflowViewState.IdRef="Sequence_6">
      <Sequence.Variables>
        <Variable x:TypeArguments="njl:JToken" Name="JOutput" />
      </Sequence.Variables>
      <Assign DisplayName="CallColeman_Assign_httpOut_3" sap2010:WorkflowViewState.IdRef="Assign_1">
        <Assign.To>
          <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="njl:JToken">[{}]</InArgument>
        </Assign.Value>
      </Assign>
      <If Condition="[DictOcrValues(&quot;TOTAL&quot;).Tostring &lt;&gt; &quot;-999&quot; AND APIString &lt;&gt; &quot;&quot;]" DisplayName="CallColeman_If_4" sap2010:WorkflowViewState.IdRef="If_3">
        <If.Then>
          <Sequence DisplayName="CallColeman_Sequence_5" sap2010:WorkflowViewState.IdRef="Sequence_4">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="ColmanTemplate" />
              <Variable x:TypeArguments="njl:JToken" Name="JInput" />
              <Variable x:TypeArguments="x:Int32" Name="colResp" />
              <Variable x:TypeArguments="x:String" Name="variable1" />
              <Variable x:TypeArguments="iru:ResponseObject" Name="RespOutput" />
            </Sequence.Variables>
            <Assign DisplayName="CallColeman_Assign_APIString_6" sap2010:WorkflowViewState.IdRef="Assign_3">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[APIString+"]"]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_Template_Apply_7" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'runtimeData': {{%APIInputArray%}},'executionType': 'MULTIPLE', 'customSchema': {}}" Text="[ColmanTemplate]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>APIInputArray</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>APIString</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_DeserializeJSON_8" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[JInput]" JTokenString="[ColmanTemplate]" />
            <iad:CommentOut DisplayName="CallColeman_CommentOut_9" sap2010:WorkflowViewState.IdRef="CommentOut_1">
              <iad:CommentOut.Activities>
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_MessageBox_10" sap2010:WorkflowViewState.IdRef="MessageBox_3" Selection="OK" Text="[JInput.tostring]" />
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="CallColeman_IONAPIRequestWizard_11" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[JInput.ToString]" Response="[respOutput]" StatusCode="[colResp]" Url="[colemanAPI]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>Accept</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>application/json</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
            </iai:IONAPIRequestWizard>
            <iad:CommentOut DisplayName="CallColeman_CommentOut_12">
              <iad:CommentOut.Activities>
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_MessageBox_13" sap2010:WorkflowViewState.IdRef="MessageBox_4" Selection="OK" Text="[respOutput.ReadAsText]" />
              </iad:CommentOut.Activities>
              <sap2010:WorkflowViewState.IdRef>CommentOut_2</sap2010:WorkflowViewState.IdRef>
            </iad:CommentOut>
            <Assign DisplayName="CallColeman_Assign_httpOut_14" sap2010:WorkflowViewState.IdRef="Assign_5">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[{}]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[colResp &lt;&gt; 200]" DisplayName="CallColeman_If_15" sap2010:WorkflowViewState.IdRef="If_2">
              <If.Then>
                <Sequence DisplayName="CallColeman_Sequence_16" sap2010:WorkflowViewState.IdRef="Sequence_1">
                  <Assign DisplayName="CallColeman_Assign_comments_17" sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">Optimizer API failure.</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="CallColeman_Assign_Status_18" sap2010:WorkflowViewState.IdRef="Assign_7">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">failure</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_Append_Line_19" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[comments]" Source="[logfile]" />
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence DisplayName="CallColeman_Sequence_20" sap2010:WorkflowViewState.IdRef="Sequence_8">
                  <Assign DisplayName="CallColeman_Assign_JOutput_21" sap2010:WorkflowViewState.IdRef="Assign_4">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[JOutput]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(RespOutput.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[JOutput(&quot;result&quot;).ToString &lt;&gt; &quot;&quot;]" DisplayName="CallColeman_If_22" sap2010:WorkflowViewState.IdRef="If_1">
                    <If.Then>
                      <Sequence DisplayName="CallColeman_Sequence_23" sap2010:WorkflowViewState.IdRef="Sequence_2">
                        <Assign DisplayName="CallColeman_Assign_httpOut_24" sap2010:WorkflowViewState.IdRef="Assign_8">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[{}]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="CallColeman_Assign_httpOut_25" sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JOutput("result")("output_data")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="CallColeman_Assign_comments_26" sap2010:WorkflowViewState.IdRef="Assign_10">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Results from Optimizer Obtained"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="CallColeman_Assign_Status_27" sap2010:WorkflowViewState.IdRef="Assign_11">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Success</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_Append_Line_28" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[comments]" Source="[logfile]" />
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence DisplayName="CallColeman_Sequence_29" sap2010:WorkflowViewState.IdRef="Sequence_3">
                        <Assign DisplayName="CallColeman_Assign_comments_30" sap2010:WorkflowViewState.IdRef="Assign_16">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Optimizer Response is not obtained</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="CallColeman_Assign_Status_31" sap2010:WorkflowViewState.IdRef="Assign_17">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">failure</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="CallColeman_Append_Line_32" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[comments]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
        <If.Else>
          <Sequence DisplayName="CallColeman_Sequence_33" sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Assign DisplayName="CallColeman_Assign_comments_34" sap2010:WorkflowViewState.IdRef="Assign_14">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[comments + Environment.NewLine + "Given PO number has no unmatched lines"]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign DisplayName="CallColeman_Assign_Status_35" sap2010:WorkflowViewState.IdRef="Assign_15">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">Failure</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </If.Else>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>d1ZDOlxVc2Vyc1xtcGF0aGFrb3RhXEFwcERhdGFcTG9jYWxcSW5mb3JSUEFcTTNJbnZvaWNlUHJvY2Vzc2luZ0dlbkFJVjNcQ2FsbENvbGVtYW4ueGFtbE5HA4oCDgIBAUgFiAIQAgECTAdTEAIBalQHhwIMAgEDUTRROAIBbU41Tj4CAWtUFVSAAQIBBFYL8QEWAgEQ9AELhQIWAgEHXg1lFgIBZWYNcSICAWFyDXLoAQIBXHMNdx4CAVt4DYMBJwIBU4QBDYkBHgIBUooBDZEBFgIBTpIBDfABEgIBEfUBDfwBFgIBDP0BDYQCFgIBCGM4Y0cCAWhgOWBEAgFmZpICZqQCAgFjZrQBZowCAgFictMBcuUBAgFfcrsBcsUBAgFdeK4CeMECAgFaeMsCeNkCAgFYePUCeIMDAgFWeOUCePACAgFUjwE6jwE+AgFRjAE7jAFEAgFPkgEbkgEzAgESlAERpgEcAgFAqQER7gEcAgEU+gE4+gGDAQIBD/cBOfcBQwIBDYICOIICPwIBC/8BOf8BQQIBCZUBE5wBHAIBSp0BE6QBHAIBRqUBE6UB0AECAUGqAROxARwCATyyARPtARgCARWaAT6aAVQCAU2XAT+XAUkCAUuiAT6iAUUCAUmfAT+fAUcCAUelAa4BpQG6AQIBRKUBwgGlAc0BAgFCrwFArwFlAgE/rAFBrAFKAgE9sgEhsgFfAgEWtAEX1gEiAgEl2QEX6wEiAgEXtQEZvAEiAgE4vQEZxAEiAgEzxQEZzAEiAgEvzQEZ1AEiAgEr1QEZ1QHWAQIBJtoBGeEBIgIBIeIBGekBIgIBHeoBGeoB1gECARi6AUa6AUoCATu3AUe3AVACATnCAUbCAWgCATa/AUe/AVACATTKAUTKAWcCATLHAUXHAU8CATDSAUTSAUsCAS7PAUXPAU0CASzVAbQB1QHAAQIBKdUByAHVAdMBAgEn3wFE3wFmAgEk3AFF3AFPAgEi5wFE5wFLAgEg5AFF5AFNAgEe6gG0AeoBwAECARvqAcgB6gHTAQIBGQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1178,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="866,62" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="866,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="866,22" />
      <sap2010:ViewStateData Id="MessageBox_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="866,118" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="866,22" />
      <sap2010:ViewStateData Id="MessageBox_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="866,118" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="866,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,554">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="554,708" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="576,934">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="866,1088" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="888,1918">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1178,2072">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="1200,2298">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="1222,2422">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1262,2662" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
