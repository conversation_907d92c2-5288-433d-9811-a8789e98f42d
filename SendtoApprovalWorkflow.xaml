﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="amount" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="inbn" Type="InArgument(x:String)" />
    <x:Property Name="invoiceNumber" Type="InArgument(x:String)" />
    <x:Property Name="poNumber" Type="InArgument(x:String)" />
    <x:Property Name="vendorName" Type="InArgument(x:String)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="id" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SendtoApprovalWorkflow_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="requestStr" />
      <Variable x:TypeArguments="njl:JToken" Name="requestJToken" />
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="responseObject" />
    </Sequence.Variables>
    <Assign DisplayName="SendtoApprovalWorkflow_Assign_id_2" sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[id]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendtoApprovalWorkflow_Template_Apply_3" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'workflowName':'{{%workflowName%}}','inputVariables':[{'name':'Amount','dataType':'DECIMAL','value':'{{%amount%}}'},{'name':'division','dataType':'STRING','value':'{{%division%}}'},{'name':'inbn','dataType':'STRING','value':'{{%inbn%}}'},{'name':'invoiceNumber','dataType':'STRING','value':'{{%invoiceNumber%}}'},{'name':'poNumber','dataType':'STRING','value':'{{%poNumber%}}'},{'name':'vendorName','dataType':'STRING','value':'{{%vendorName%}}'}]}" Text="[requestStr]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>workflowName</x:String>
            <x:String>amount</x:String>
            <x:String>division</x:String>
            <x:String>inbn</x:String>
            <x:String>invoiceNumber</x:String>
            <x:String>poNumber</x:String>
            <x:String>vendorName</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>approvalWorkflow</x:String>
            <x:String>amount</x:String>
            <x:String>division</x:String>
            <x:String>inbn</x:String>
            <x:String>invoiceNumber</x:String>
            <x:String>poNumber</x:String>
            <x:String>vendorName</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SendtoApprovalWorkflow_DeserializeJSON_4" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[requestJToken]" JTokenString="[requestStr]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SendtoApprovalWorkflow_IONAPIRequestWizard_5" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[requestJToken.tostring]" Response="[responseObject]" StatusCode="[responseCode]" Url="[TenantID + &quot;IONSERVICES/process/application/v1/workflow/start?logicalId=lid%3A%2F%2Finfor.test.test&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <sads:DebugSymbol.Symbol>d2JDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFNlbmR0b0FwcHJvdmFsV29ya2Zsb3cueGFtbA9GA30OAgEBTQVWDgIBE1cFbhoCAQ9vBW/WAQIBCnAFex8CAQJTC1M6AgEWTzFPNQIBFFfuBFf8BAIBEVejAVfoBAIBEG/FAW/TAQIBDW+mAW+3AQIBC3CSAnCsAgIBCXC2AnDIAgIBB3DpAnDbAwIBBXDUAnDkAgIBAw==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,370">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="304,450" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
