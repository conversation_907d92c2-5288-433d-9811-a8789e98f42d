# Ultra-comprehensive PowerShell script to update ALL DisplayName attributes in XAML files
# Guarantees 100% coverage including activities in If/Else, loops, Try/Catch, Switch, nested sequences, flowcharts, etc.
# Adds DisplayName to activities that don't have them and updates existing ones
# Naming convention: XAMLName_ActivityName_XamlLevelCounter
# Special rule for Assign: XAMLName_Assign_VariableName_XamlLevelCounter

param(
    [string]$XamlFilePath
)

function Update-XamlDisplayNamesUltraComprehensive {
    param(
        [string]$FilePath
    )

    Write-Host "Processing file: $FilePath"

    # Get the XAML file name without extension
    $xamlName = [System.IO.Path]::GetFileNameWithoutExtension($FilePath)

    # Read and parse the XML content
    try {
        $xml = New-Object System.Xml.XmlDocument
        $xml.PreserveWhitespace = $true
        $xml.Load($FilePath)
    }
    catch {
        Write-Error "Failed to parse XML in file: $FilePath - $($_.Exception.Message)"
        return
    }

    # Global counter for activities in this file
    $script:activityCounter = 1
    $script:updatedCount = 0
    $script:addedCount = 0
    
    # Function to extract variable name from Assign activity
    function Get-AssignVariableName {
        param([System.Xml.XmlElement]$assignElement)
        
        try {
            # Multiple strategies to find the variable name
            
            # Strategy 1: Look for OutArgument in Assign.To
            $assignTo = $assignElement.SelectSingleNode("*[local-name()='Assign.To']")
            if ($assignTo) {
                $outArgument = $assignTo.SelectSingleNode("*[local-name()='OutArgument']")
                if ($outArgument -and $outArgument.InnerText) {
                    $varText = $outArgument.InnerText.Trim()
                    if ($varText -match '\[([^\]]+)\]') {
                        $varName = $matches[1]
                        # If it's a property access, get just the variable name
                        if ($varName -match '^([^.(]+)') {
                            return $matches[1]
                        }
                        return $varName
                    }
                }
            }
            
            # Strategy 2: Look for To attribute directly on Assign element
            if ($assignElement.HasAttribute("To")) {
                $toValue = $assignElement.GetAttribute("To")
                if ($toValue -match '\[([^\]]+)\]') {
                    $varName = $matches[1]
                    if ($varName -match '^([^.(]+)') {
                        return $matches[1]
                    }
                    return $varName
                }
            }
            
            # Strategy 3: Look in child elements for variable references
            foreach ($child in $assignElement.ChildNodes) {
                if ($child.NodeType -eq [System.Xml.XmlNodeType]::Element) {
                    $childText = $child.InnerText
                    if ($childText -match '\[([^\]]+)\]') {
                        $varName = $matches[1]
                        if ($varName -match '^([^.(]+)') {
                            return $matches[1]
                        }
                        return $varName
                    }
                }
            }
        }
        catch {
            # If we can't extract the variable name, return null
        }
        return $null
    }
    
    # Function to check if an element is a UiPath activity that should have DisplayName
    function Is-UiPathActivity {
        param([System.Xml.XmlElement]$element)

        # List of common UiPath activity types that should have DisplayName
        $activityTypes = @(
            'Assign', 'Sequence', 'If', 'ForEach', 'While', 'Switch', 'TryCatch', 'Catch', 'Finally',
            'InvokeWorkflow', 'InvokeMethod', 'Flowchart', 'FlowStep', 'FlowDecision', 'FlowSwitch',
            'Parallel', 'ParallelForEach', 'DoWhile', 'Pick', 'PickBranch', 'StateMachine', 'State',
            'Transition', 'CommentOut', 'Append_Line', 'File_Delete', 'File_Read', 'File_Create',
            'Directory_Create', 'Directory_Delete', 'Path_Validate', 'DownloadFile_URL', 'JQTransform',
            'ReadRange', 'WriteRange', 'IONAPIRequestWizard', 'MessageBox', 'LogMessage', 'WriteLine',
            'Delay', 'TerminateWorkflow', 'Rethrow', 'Throw'
        )

        $localName = $element.LocalName

        # Remove namespace prefix if present
        if ($localName -match ':') {
            $localName = $localName.Split(':')[-1]
        }

        # Check if it's a known activity type or has sap2010:WorkflowViewState.IdRef (UiPath activity indicator)
        return ($activityTypes -contains $localName) -or $element.HasAttribute("sap2010:WorkflowViewState.IdRef")
    }

    # Function to recursively process ALL XML elements with maximum depth coverage
    function Process-XmlElementRecursive {
        param(
            [System.Xml.XmlNode]$node
        )

        # Only process XML elements (not text nodes, comments, etc.)
        if ($node.NodeType -eq [System.Xml.XmlNodeType]::Element) {
            $element = [System.Xml.XmlElement]$node

            # Check if this is a UiPath activity that should have DisplayName
            if (Is-UiPathActivity -element $element) {
                $activityName = $element.LocalName

                # Remove namespace prefix if present
                if ($activityName -match ':') {
                    $activityName = $activityName.Split(':')[-1]
                }

                # Special handling for Assign activities
                if ($activityName -eq "Assign") {
                    $varName = Get-AssignVariableName -assignElement $element
                    if ($varName) {
                        $newDisplayName = "${xamlName}_Assign_${varName}_$script:activityCounter"
                    } else {
                        $newDisplayName = "${xamlName}_Assign_$script:activityCounter"
                    }
                } else {
                    $newDisplayName = "${xamlName}_${activityName}_$script:activityCounter"
                }

                # Check if DisplayName already exists
                if ($element.HasAttribute("DisplayName")) {
                    # Update existing DisplayName
                    $oldDisplayName = $element.GetAttribute("DisplayName")
                    $element.SetAttribute("DisplayName", $newDisplayName)
                    $script:updatedCount++
                    Write-Verbose "Updated: $($element.LocalName) [$oldDisplayName] -> [$newDisplayName]"
                } else {
                    # Add new DisplayName attribute
                    $element.SetAttribute("DisplayName", $newDisplayName)
                    $script:addedCount++
                    Write-Verbose "Added: $($element.LocalName) -> [$newDisplayName]"
                }

                $script:activityCounter++
            }
        }
        
        # Recursively process ALL child nodes - this ensures we catch everything
        if ($node.HasChildNodes) {
            foreach ($childNode in $node.ChildNodes) {
                Process-XmlElementRecursive -node $childNode
            }
        }
    }
    
    # Start processing from the document root to ensure we catch everything
    if ($xml.DocumentElement) {
        Process-XmlElementRecursive -node $xml.DocumentElement
    }
    
    # Also process any other top-level nodes (just in case)
    foreach ($topNode in $xml.ChildNodes) {
        if ($topNode.NodeType -eq [System.Xml.XmlNodeType]::Element -and $topNode -ne $xml.DocumentElement) {
            Process-XmlElementRecursive -node $topNode
        }
    }
    
    # Save the updated XML with preserved formatting
    try {
        $settings = New-Object System.Xml.XmlWriterSettings
        $settings.Indent = $true
        $settings.IndentChars = "  "
        $settings.NewLineChars = "`r`n"
        $settings.Encoding = [System.Text.Encoding]::UTF8
        $settings.OmitXmlDeclaration = $false
        
        $writer = [System.Xml.XmlWriter]::Create($FilePath, $settings)
        $xml.Save($writer)
        $writer.Close()
        
        Write-Host "Successfully processed: $FilePath"
        Write-Host "  - Updated existing DisplayNames: $script:updatedCount"
        Write-Host "  - Added new DisplayNames: $script:addedCount"
        Write-Host "  - Total activities processed: $($script:updatedCount + $script:addedCount)"
    }
    catch {
        Write-Error "Failed to save file: $FilePath - $($_.Exception.Message)"
    }
}

# Main execution
if ($XamlFilePath) {
    if (Test-Path $XamlFilePath) {
        Update-XamlDisplayNamesUltraComprehensive -FilePath $XamlFilePath
    } else {
        Write-Error "File not found: $XamlFilePath"
    }
} else {
    # Process all XAML files in current directory
    $xamlFiles = Get-ChildItem -Path "." -Filter "*.xaml" | Sort-Object Name
    $totalFiles = $xamlFiles.Count
    $currentFile = 0
    $grandTotal = 0
    
    Write-Host "Found $totalFiles XAML files to process..."
    Write-Host "Starting ultra-comprehensive DisplayName update..."
    
    foreach ($file in $xamlFiles) {
        $currentFile++
        Write-Host "[$currentFile/$totalFiles] Processing: $($file.Name)"
        $script:updatedCount = 0
        $script:addedCount = 0
        Update-XamlDisplayNamesUltraComprehensive -FilePath $file.FullName
        $grandTotal += ($script:updatedCount + $script:addedCount)
    }
    
    Write-Host ""
    Write-Host "=== ULTRA-COMPREHENSIVE UPDATE COMPLETE ==="
    Write-Host "Processed: $totalFiles XAML files"
    Write-Host "Total activities updated: $grandTotal"
    Write-Host "============================================"
}
