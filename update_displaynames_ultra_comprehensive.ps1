# Ultra-comprehensive PowerShell script to update ALL DisplayName attributes in XAML files
# Guarantees 100% coverage including activities in If/Else, loops, Try/Catch, Switch, nested sequences, flowcharts, etc.
# Naming convention: XAMLName_ActivityName_XamlLevelCounter
# Special rule for Assign: XAMLName_Assign_VariableName_XamlLevelCounter

param(
    [string]$XamlFilePath
)

function Update-XamlDisplayNamesUltraComprehensive {
    param(
        [string]$FilePath
    )
    
    Write-Host "Processing file: $FilePath"
    
    # Get the XAML file name without extension
    $xamlName = [System.IO.Path]::GetFileNameWithoutExtension($FilePath)
    
    # Read and parse the XML content
    try {
        $xml = New-Object System.Xml.XmlDocument
        $xml.PreserveWhitespace = $true
        $xml.Load($FilePath)
    }
    catch {
        Write-Error "Failed to parse XML in file: $FilePath - $($_.Exception.Message)"
        return
    }
    
    # Global counter for activities in this file
    $script:activityCounter = 1
    $script:updatedCount = 0
    
    # Function to extract variable name from Assign activity
    function Get-AssignVariableName {
        param([System.Xml.XmlElement]$assignElement)
        
        try {
            # Multiple strategies to find the variable name
            
            # Strategy 1: Look for OutArgument in Assign.To
            $assignTo = $assignElement.SelectSingleNode("*[local-name()='Assign.To']")
            if ($assignTo) {
                $outArgument = $assignTo.SelectSingleNode("*[local-name()='OutArgument']")
                if ($outArgument -and $outArgument.InnerText) {
                    $varText = $outArgument.InnerText.Trim()
                    if ($varText -match '\[([^\]]+)\]') {
                        $varName = $matches[1]
                        # If it's a property access, get just the variable name
                        if ($varName -match '^([^.(]+)') {
                            return $matches[1]
                        }
                        return $varName
                    }
                }
            }
            
            # Strategy 2: Look for To attribute directly on Assign element
            if ($assignElement.HasAttribute("To")) {
                $toValue = $assignElement.GetAttribute("To")
                if ($toValue -match '\[([^\]]+)\]') {
                    $varName = $matches[1]
                    if ($varName -match '^([^.(]+)') {
                        return $matches[1]
                    }
                    return $varName
                }
            }
            
            # Strategy 3: Look in child elements for variable references
            foreach ($child in $assignElement.ChildNodes) {
                if ($child.NodeType -eq [System.Xml.XmlNodeType]::Element) {
                    $childText = $child.InnerText
                    if ($childText -match '\[([^\]]+)\]') {
                        $varName = $matches[1]
                        if ($varName -match '^([^.(]+)') {
                            return $matches[1]
                        }
                        return $varName
                    }
                }
            }
        }
        catch {
            # If we can't extract the variable name, return null
        }
        return $null
    }
    
    # Function to recursively process ALL XML elements with maximum depth coverage
    function Process-XmlElementRecursive {
        param(
            [System.Xml.XmlNode]$node
        )
        
        # Only process XML elements (not text nodes, comments, etc.)
        if ($node.NodeType -eq [System.Xml.XmlNodeType]::Element) {
            $element = [System.Xml.XmlElement]$node
            
            # Check if this element has a DisplayName attribute
            if ($element.HasAttribute("DisplayName")) {
                $activityName = $element.LocalName
                
                # Remove namespace prefix if present
                if ($activityName -match ':') {
                    $activityName = $activityName.Split(':')[-1]
                }
                
                # Special handling for Assign activities
                if ($activityName -eq "Assign") {
                    $varName = Get-AssignVariableName -assignElement $element
                    if ($varName) {
                        $newDisplayName = "${xamlName}_Assign_${varName}_$script:activityCounter"
                    } else {
                        $newDisplayName = "${xamlName}_Assign_$script:activityCounter"
                    }
                } else {
                    $newDisplayName = "${xamlName}_${activityName}_$script:activityCounter"
                }
                
                # Update the DisplayName attribute
                $oldDisplayName = $element.GetAttribute("DisplayName")
                $element.SetAttribute("DisplayName", $newDisplayName)
                $script:activityCounter++
                $script:updatedCount++
                
                Write-Verbose "Updated: $($element.LocalName) [$oldDisplayName] -> [$newDisplayName]"
            }
        }
        
        # Recursively process ALL child nodes - this ensures we catch everything
        if ($node.HasChildNodes) {
            foreach ($childNode in $node.ChildNodes) {
                Process-XmlElementRecursive -node $childNode
            }
        }
    }
    
    # Start processing from the document root to ensure we catch everything
    if ($xml.DocumentElement) {
        Process-XmlElementRecursive -node $xml.DocumentElement
    }
    
    # Also process any other top-level nodes (just in case)
    foreach ($topNode in $xml.ChildNodes) {
        if ($topNode.NodeType -eq [System.Xml.XmlNodeType]::Element -and $topNode -ne $xml.DocumentElement) {
            Process-XmlElementRecursive -node $topNode
        }
    }
    
    # Save the updated XML with preserved formatting
    try {
        $settings = New-Object System.Xml.XmlWriterSettings
        $settings.Indent = $true
        $settings.IndentChars = "  "
        $settings.NewLineChars = "`r`n"
        $settings.Encoding = [System.Text.Encoding]::UTF8
        $settings.OmitXmlDeclaration = $false
        
        $writer = [System.Xml.XmlWriter]::Create($FilePath, $settings)
        $xml.Save($writer)
        $writer.Close()
        
        Write-Host "Successfully updated: $FilePath with $script:updatedCount activities"
    }
    catch {
        Write-Error "Failed to save file: $FilePath - $($_.Exception.Message)"
    }
}

# Main execution
if ($XamlFilePath) {
    if (Test-Path $XamlFilePath) {
        Update-XamlDisplayNamesUltraComprehensive -FilePath $XamlFilePath
    } else {
        Write-Error "File not found: $XamlFilePath"
    }
} else {
    # Process all XAML files in current directory
    $xamlFiles = Get-ChildItem -Path "." -Filter "*.xaml" | Sort-Object Name
    $totalFiles = $xamlFiles.Count
    $currentFile = 0
    $grandTotal = 0
    
    Write-Host "Found $totalFiles XAML files to process..."
    Write-Host "Starting ultra-comprehensive DisplayName update..."
    
    foreach ($file in $xamlFiles) {
        $currentFile++
        Write-Host "[$currentFile/$totalFiles] Processing: $($file.Name)"
        $script:updatedCount = 0
        Update-XamlDisplayNamesUltraComprehensive -FilePath $file.FullName
        $grandTotal += $script:updatedCount
    }
    
    Write-Host ""
    Write-Host "=== ULTRA-COMPREHENSIVE UPDATE COMPLETE ==="
    Write-Host "Processed: $totalFiles XAML files"
    Write-Host "Total activities updated: $grandTotal"
    Write-Host "============================================"
}
