﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug" xmlns:iad1="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web" xmlns:iro="clr-namespace:Infor.RPA.OCR;assembly=Infor.RPA.OCR" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="promptPath2" Type="InArgument(x:String)" />
    <x:Property Name="copiedFilePath" Type="InArgument(x:String)" />
    <x:Property Name="InProgressFolder" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="strSplitExtension" Type="InArgument(x:String)" />
    <x:Property Name="strClassificationExtension" Type="InArgument(x:String)" />
    <x:Property Name="DictFileNames" Type="OutArgument(scg:Dictionary(x:String, x:String))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Linq.Expressions</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="Classification_Split_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_79">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:List(x:String)" Name="MasterDownloadedFiles" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JToken" Name="values" />
      <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="invoicePageDictionary" />
      <Variable x:TypeArguments="x:String" Name="noOfPages" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListClassificationData" />
      <Variable x:TypeArguments="x:String" Name="CurrentOCRFile" />
      <Variable x:TypeArguments="x:String" Name="CurrentClassificationFIle" />
      <Variable x:TypeArguments="x:String" Name="ClassificationText" />
      <Variable x:TypeArguments="x:String" Name="OCRText" />
      <Variable x:TypeArguments="x:String" Name="FinalPages" />
      <Variable x:TypeArguments="x:String" Name="NameForInvoice" />
      <Variable x:TypeArguments="x:Boolean" Name="blnInvDictExists" />
      <Variable x:TypeArguments="x:String" Name="strCurVendorSupportingPages" />
      <Variable x:TypeArguments="x:String" Name="CurInvPageNoAll" />
      <Variable x:TypeArguments="x:Int32" Default="1" Name="UnstructuredCounter" />
      <Variable x:TypeArguments="x:String" Name="CurInvoiceNumber" />
      <Variable x:TypeArguments="x:String" Name="strVendName" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="pdfFileName" />
      <Variable x:TypeArguments="x:String" Name="outputStructure" />
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="x:String" Name="customTextSplit" />
      <Variable x:TypeArguments="x:Boolean" Name="blncustomExists1" />
    </Sequence.Variables>
    <Sequence DisplayName="Classification_Split_Sequence_2" sap2010:WorkflowViewState.IdRef="Sequence_108">
      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Classification_Split_Directory_GetFiles_3" FileType="PDF" Files="[MasterDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
      <ForEach x:TypeArguments="x:String" DisplayName="Classification_Split_ForEach_4" sap2010:WorkflowViewState.IdRef="ForEach`1_33" Values="[MasterDownloadedFiles]">
        <ActivityAction x:TypeArguments="x:String">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="x:String" Name="documentPath" />
          </ActivityAction.Argument>
          <TryCatch DisplayName="Classification_Split_TryCatch_5" sap2010:WorkflowViewState.IdRef="TryCatch_1">
            <TryCatch.Try>
              <Sequence DisplayName="Classification_Split_Sequence_6" sap2010:WorkflowViewState.IdRef="Sequence_51">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="promptText" />
                  <Variable x:TypeArguments="x:Int32" Name="responseCode" />
                  <Variable x:TypeArguments="x:String" Name="OCRtxtFile" />
                  <Variable x:TypeArguments="x:String" Name="ClassifcationtxtFile" />
                </Sequence.Variables>
                <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Read_7" sap2010:WorkflowViewState.IdRef="File_Read_4" Source="[promptPath]" Text="[promptText]" />
                <iro:DocumentOC Pages="{x:Null}" ActivityID="f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8" ContinueOnError="False" DisplayName="Classification_Split_DocumentOC_8" ErrorCode="[responseCode]" ExecuteFile="True" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="DocumentOC_2" ResponseObject="[values]" />
                <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Create_9" sap2010:WorkflowViewState.IdRef="File_Create_3" Name="[Path.GetFileName(documentPath).Substring(0, Path.GetFileName(documentPath).Length - 4)+&quot;.txt&quot;]" OutputFile="[OCRtxtFile]" Target="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" />
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Append_Line_10" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[values.ToString]" Source="[OCRtxtFile]" />
                <Switch x:TypeArguments="x:Boolean" DisplayName="Classification_Split_Switch_11" Expression="[responseCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_3">
                  <Sequence x:Key="True" DisplayName="Classification_Split_Sequence_12" sap2010:WorkflowViewState.IdRef="Sequence_50">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="promptRequest" />
                      <Variable x:TypeArguments="x:String" Name="IonBody" />
                      <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
                      <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
                      <Variable x:TypeArguments="njl:JToken" Name="out1" />
                      <Variable x:TypeArguments="x:String" Name="out2" />
                      <Variable x:TypeArguments="scg:List(x:String)" Name="txtFiles" />
                      <Variable x:TypeArguments="x:Boolean" Name="customExists" />
                      <Variable x:TypeArguments="x:String" Name="customText" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[noOfPages]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[values("_metadata")("NumberOfPages").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">
                          <Literal x:TypeArguments="x:String" Value="" />
                        </InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Path_Validate_13" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[customExists]" Path="[strClassificationExtension]" />
                    <If Condition="[customExists]" DisplayName="Classification_Split_If_14" sap2010:WorkflowViewState.IdRef="If_23">
                      <If.Then>
                        <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Read_15" sap2010:WorkflowViewState.IdRef="File_Read_5" Source="[strClassificationExtension]" Text="[customText]" />
                      </If.Then>
                    </If>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_468">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_469">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iad:CommentOut DisplayName="Classification_Split_CommentOut_16" sap2010:WorkflowViewState.IdRef="CommentOut_22">
                      <iad:CommentOut.Activities>
                        <Sequence DisplayName="Classification_Split_Sequence_17" sap2010:WorkflowViewState.IdRef="Sequence_148">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{notes}",customText.tostring)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Template_Apply_18" sap2010:WorkflowViewState.IdRef="Template_Apply_13" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':4096,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody]">
                            <ias:Template_Apply.Values>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>prompt</x:String>
                                  <x:String>model</x:String>
                                  <x:String>version</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>promptRequest</x:String>
                                  <x:String>GenAIModel</x:String>
                                  <x:String>GenAIModelVersion</x:String>
                                </scg:List>
                              </scg:List>
                            </ias:Template_Apply.Values>
                          </ias:Template_Apply>
                        </Sequence>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[ocrOutput]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[values.toString.Replace("'","")]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </iad:CommentOut.Activities>
                    </iad:CommentOut>
                    <Sequence DisplayName="Classification_Split_Sequence_19" sap2010:WorkflowViewState.IdRef="Sequence_149">
                      <Assign DisplayName="Classification_Split_Assign_promptRequest_20" sap2010:WorkflowViewState.IdRef="Assign_480">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Classification_Split_Assign_promptRequest_21" sap2010:WorkflowViewState.IdRef="Assign_481">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Classification_Split_Assign_outputStructure_22" sap2010:WorkflowViewState.IdRef="Assign_482">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Classification_Split_Assign_promptRequest_23" sap2010:WorkflowViewState.IdRef="Assign_483">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Classification_Split_Assign_value_24" sap2010:WorkflowViewState.IdRef="Assign_484">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iad:CommentOut DisplayName="Classification_Split_CommentOut_25" sap2010:WorkflowViewState.IdRef="CommentOut_25">
                        <iad:CommentOut.Activities>
                          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Template_Apply_26" sap2010:WorkflowViewState.IdRef="Template_Apply_14" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the questions using the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                            <ias:Template_Apply.Values>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                  <x:String>prompt</x:String>
                                  <x:String>model</x:String>
                                  <x:String>version</x:String>
                                  <x:String>ocrText</x:String>
                                  <x:String>outputStructure</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                  <x:String>promptRequest</x:String>
                                  <x:String>GenAIModel</x:String>
                                  <x:String>GenAIModelVersion</x:String>
                                  <x:String>value</x:String>
                                  <x:String>outputStructure</x:String>
                                </scg:List>
                              </scg:List>
                            </ias:Template_Apply.Values>
                          </ias:Template_Apply>
                        </iad:CommentOut.Activities>
                      </iad:CommentOut>
                      <ias:Template_Apply ErrorCode="{x:Null}" sap2010:Annotation.AnnotationText="Updated Templating" ContinueOnError="True" DisplayName="Classification_Split_Template_Apply_27" sap2010:WorkflowViewState.IdRef="Template_Apply_16" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are expert document based question answering tool. You will be provided with the raw text of the document using textract. You are to identify the page numbers for a specific vendor where and all there are invoice related pages available. You will also be provided with some instructions on how to identify the correct page numbers for a given vendor from the document. Make sure to understand the provided example to correctly arrive at the invoice and supporting page numbers from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                        <ias:Template_Apply.Values>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="8">
                              <x:String>prompt</x:String>
                              <x:String>model</x:String>
                              <x:String>version</x:String>
                              <x:String>ocrText</x:String>
                              <x:String>outputStructure</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="8">
                              <x:String>promptRequest</x:String>
                              <x:String>GenAIModel</x:String>
                              <x:String>GenAIModelVersion</x:String>
                              <x:String>value</x:String>
                              <x:String>outputStructure</x:String>
                            </scg:List>
                          </scg:List>
                        </ias:Template_Apply.Values>
                      </ias:Template_Apply>
                    </Sequence>
                    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_DeserializeJSON_28" sap2010:WorkflowViewState.IdRef="DeserializeJSON_9" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Classification_Split_IONAPIRequestWizard_29" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>x-infor-logicalidprefix</x:String>
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>lid://infor.colemanddp</x:String>
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="0" />
                          <scg:List x:TypeArguments="x:String" Capacity="0" />
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
                      <Assign.To>
                        <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_DeserializeJSON_30" sap2010:WorkflowViewState.IdRef="DeserializeJSON_10" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
                    <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Create_31" sap2010:WorkflowViewState.IdRef="File_Create_4" Name="[Path.GetFileName(documentPath).Substring(0, Path.GetFileName(documentPath).Length - 4)+&quot;.txt&quot;]" OutputFile="[ClassifcationtxtFile]" Target="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" />
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_Append_Line_32" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[jout.ToString]" Source="[ClassifcationtxtFile]" />
                  </Sequence>
                </Switch>
              </Sequence>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Append_Line_33" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[&quot;Error occurred during OCR Extraction and Classification for file: &quot;+Path.GetFileName(documentPath)+Environment.NewLine+exception.message]" Source="[logFile]" />
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_495">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[DictFileNames]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[New Dictionary(Of String,String)]</InArgument>
      </Assign.Value>
    </Assign>
    <Switch x:TypeArguments="x:Boolean" DisplayName="Classification_Split_Switch_34" Expression="[miscValues(&quot;SplittingDoc&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="Switch`1_27">
      <Sequence x:Key="True" DisplayName="Classification_Split_Sequence_35" sap2010:WorkflowViewState.IdRef="Sequence_109">
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Classification_Split_Directory_GetFiles_36" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_2" IncludeSubDir="True" />
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" DisplayName="Classification_Split_Directory_GetFiles_37" FileType="All" Files="[ListClassificationData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="Classification_Split_ForEach_38" sap2010:WorkflowViewState.IdRef="ForEach`1_40" Values="[MasterDownloadedFiles]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="item" />
            </ActivityAction.Argument>
            <TryCatch DisplayName="Classification_Split_TryCatch_39" sap2010:WorkflowViewState.IdRef="TryCatch_2">
              <TryCatch.Try>
                <Sequence DisplayName="Classification_Split_Sequence_40" sap2010:WorkflowViewState.IdRef="Sequence_169">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                    <Variable x:TypeArguments="x:String" Name="strSpecificText" />
                    <Variable x:TypeArguments="x:String" Name="strMergeFileName" />
                    <Variable x:TypeArguments="x:String" Name="strInvoiceExistsCondition" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_509">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[New Dictionary (Of String, String)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_510">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListOCRData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_511">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[CurrentClassificationFIle]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListClassificationData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Read_41" sap2010:WorkflowViewState.IdRef="File_Read_28" Source="[CurrentOCRFile]" Text="[OCRText]" />
                  <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Read_42" sap2010:WorkflowViewState.IdRef="File_Read_29" Source="[CurrentClassificationFIle]" Text="[ClassificationText]" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_512">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[values]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_513">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[Jout]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ClassificationText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="njl:JProperty" DisplayName="Classification_Split_ForEach_43" sap2010:WorkflowViewState.IdRef="ForEach`1_63" Values="[(JObject.Parse(jout.ToString)).Properties()]">
                    <ActivityAction x:TypeArguments="njl:JProperty">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="njl:JProperty" Name="Joutitem" />
                      </ActivityAction.Argument>
                      <Sequence DisplayName="Classification_Split_Sequence_44" sap2010:WorkflowViewState.IdRef="Sequence_164">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="njl:JToken" Name="ReqOCRvalues" />
                          <Variable x:TypeArguments="x:String" Name="targetPages" />
                        </Sequence.Variables>
                        <iad:CommentOut DisplayName="Classification_Split_CommentOut_45" sap2010:WorkflowViewState.IdRef="CommentOut_29">
                          <iad:CommentOut.Activities>
                            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_MessageBox_46" sap2010:WorkflowViewState.IdRef="MessageBox_74" Selection="OK" Text="[Joutitem.tostring]" Title="Joutitem Value from Classification  - 1" />
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <Switch x:TypeArguments="x:String" DisplayName="Classification_Split_Switch_47" Expression="[Joutitem.Value(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_30">
                          <Sequence x:Key="Invoice" DisplayName="Classification_Split_Sequence_48" sap2010:WorkflowViewState.IdRef="Sequence_160">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="Page" />
                              <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                              <Variable x:TypeArguments="x:String" Name="base64string" />
                              <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                              <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                              <Variable x:TypeArguments="x:String" Name="IonBody1" />
                              <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                              <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                              <Variable x:TypeArguments="x:String" Name="out4" />
                              <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_514">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strVendName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Joutitem.Name.Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Sequence DisplayName="Classification_Split_Sequence_49" sap2010:WorkflowViewState.IdRef="Sequence_156">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_515">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_516">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[targetPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[String.Join(",", CType(Joutitem.Value(1), JArray).Select(Function(x) x.ToString()))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_517">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="s:String[]">[ArrayTargetPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="s:String[]">[targetPages.Split(","c).Select(Function(x) x.Trim()).ToArray()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_518">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[JsonConvert.SerializeObject(
    ReqOCRvalues("data").
        Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
        Select(Function(p) New With {
            Key .OCR_text = p("OCR_text").ToString(),
            Key .PageNo = p("PageNo").ToString()
        })
)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <iad:CommentOut DisplayName="Classification_Split_CommentOut_50" sap2010:WorkflowViewState.IdRef="CommentOut_30">
                                <iad:CommentOut.Activities>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_519">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[String.Join(Environment.NewLine,
  ReqOCRvalues("data").
    Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
    Select(Function(p) p("OCR_text").ToString()))]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </iad:CommentOut.Activities>
                              </iad:CommentOut>
                            </Sequence>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_520">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[page]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Joutitem.Value(1)(0).Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="Classification_Split_OpenBrowser_51" sap2010:WorkflowViewState.IdRef="OpenBrowser_9" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
                            <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_NavigateTo_52" sap2010:WorkflowViewState.IdRef="NavigateTo_9" URL="[Path.GetDirectoryName(item) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(item)) + &quot;#page=&quot;+page]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
                            <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_MaximizeWindow_53" sap2010:WorkflowViewState.IdRef="MaximizeWindow_9" WaitAfter="0" WaitBefore="0" />
                            <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_SendKeys_54" sap2010:WorkflowViewState.IdRef="SendKeys_25" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
                            <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_SendKeys_55" sap2010:WorkflowViewState.IdRef="SendKeys_26" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                            <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_SendKeys_56" sap2010:WorkflowViewState.IdRef="SendKeys_27" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                            <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_ScreenShot_57" sap2010:WorkflowViewState.IdRef="ScreenShot_9" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
                            <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="Classification_Split_FileToBase64_58" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_9" />
                            <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_CloseBrowser_59" sap2010:WorkflowViewState.IdRef="CloseBrowser_9" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_521">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[customTextSplit]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Path_Validate_60" sap2010:WorkflowViewState.IdRef="Path_Validate_4" IsValid="[blncustomExists1]" Path="[strSplitExtension]" />
                            <If Condition="[blncustomExists1]" sap2010:WorkflowViewState.IdRef="If_73">
                              <If.Then>
                                <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Read_61" sap2010:WorkflowViewState.IdRef="File_Read_30" Source="[strSplitExtension]" Text="[customTextSplit]" />
                              </If.Then>
                            </If>
                            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Read_62" sap2010:WorkflowViewState.IdRef="File_Read_31" Source="[promptPath2]" Text="[Split2promptText]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_522">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_523">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <iad:CommentOut DisplayName="Classification_Split_CommentOut_63" sap2010:WorkflowViewState.IdRef="CommentOut_31">
                              <iad:CommentOut.Activities>
                                <Sequence DisplayName="Classification_Split_Sequence_64" sap2010:WorkflowViewState.IdRef="Sequence_157">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_524">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Split2promptText.Replace("{text}",strSpecificText)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_525">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Replace("{prompt1_response}",Joutitem.tostring)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_526">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Replace("'","\'")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Template_Apply_65" sap2010:WorkflowViewState.IdRef="Template_Apply_17" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':4096,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody1]">
                                    <ias:Template_Apply.Values>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>prompt</x:String>
                                          <x:String>base64string</x:String>
                                          <x:String>model</x:String>
                                          <x:String>version</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>SplitpromptRequest</x:String>
                                          <x:String>base64string</x:String>
                                          <x:String>GenAIModel</x:String>
                                          <x:String>GenAIModelVersion</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </ias:Template_Apply.Values>
                                  </ias:Template_Apply>
                                </Sequence>
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                            <Sequence DisplayName="Classification_Split_Sequence_66" sap2010:WorkflowViewState.IdRef="Sequence_158">
                              <Assign DisplayName="Classification_Split_Assign_SplitpromptRequest_67" sap2010:WorkflowViewState.IdRef="Assign_527">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Split2promptText.Replace("{notes}",customTextSplit.tostring)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Classification_Split_Assign_SplitpromptRequest_68" sap2010:WorkflowViewState.IdRef="Assign_528">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Replace("'","\'")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Classification_Split_Assign_outputStructure_69" sap2010:WorkflowViewState.IdRef="Assign_529">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Classification_Split_Assign_SplitpromptRequest_70" sap2010:WorkflowViewState.IdRef="Assign_530">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Classification_Split_Assign_value_71" sap2010:WorkflowViewState.IdRef="Assign_531">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Regex.Replace(strSpecificText.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Template_Apply_72" sap2010:WorkflowViewState.IdRef="Template_Apply_18" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody1]">
                                <ias:Template_Apply.Values>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                      <x:String>prompt</x:String>
                                      <x:String>base64string</x:String>
                                      <x:String>model</x:String>
                                      <x:String>version</x:String>
                                      <x:String>ocrText</x:String>
                                      <x:String>outputStructure</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                      <x:String>SplitpromptRequest</x:String>
                                      <x:String>base64string</x:String>
                                      <x:String>GenAIModel</x:String>
                                      <x:String>GenAIModelVersion</x:String>
                                      <x:String>value</x:String>
                                      <x:String>outputStructure</x:String>
                                    </scg:List>
                                  </scg:List>
                                </ias:Template_Apply.Values>
                              </ias:Template_Apply>
                            </Sequence>
                            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_DeserializeJSON_73" sap2010:WorkflowViewState.IdRef="DeserializeJSON_26" JTokenObject="[SplitgenAIRequestToken]" JTokenString="[IonBody1]" />
                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Classification_Split_IONAPIRequestWizard_74" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_12" PostData="[SplitgenAIRequestToken]" Response="[genAIResponse1]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
                              <iai:IONAPIRequestWizard.Headers>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>x-infor-logicalidprefix</x:String>
                                    <x:String>Accept</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                    <x:String>lid://infor.colemanddp</x:String>
                                    <x:String>application/json</x:String>
                                  </scg:List>
                                </scg:List>
                              </iai:IONAPIRequestWizard.Headers>
                              <iai:IONAPIRequestWizard.QueryParameters>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                                </scg:List>
                              </iai:IONAPIRequestWizard.QueryParameters>
                            </iai:IONAPIRequestWizard>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_532">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse1.ReadAsText)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_533">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[out3("content").ToString.Replace("\n","")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_534">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[(out4.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <iad:CommentOut DisplayName="Classification_Split_CommentOut_75" sap2010:WorkflowViewState.IdRef="CommentOut_32">
                              <iad:CommentOut.Activities>
                                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_MessageBox_76" sap2010:WorkflowViewState.IdRef="MessageBox_75" Selection="OK" Text="[out4.ToString]" Title="Split Before Deserialize 4" />
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_DeserializeJSON_77" sap2010:WorkflowViewState.IdRef="DeserializeJSON_27" JTokenObject="[jout1]" JTokenString="[out4.ToString]" />
                            <iad:CommentOut DisplayName="Classification_Split_CommentOut_78" sap2010:WorkflowViewState.IdRef="CommentOut_33">
                              <iad:CommentOut.Activities>
                                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_MessageBox_79" sap2010:WorkflowViewState.IdRef="MessageBox_76" Selection="OK" Text="[jout1.tostring]" Title="Split2 Output - Deserialize JSON4" />
                                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_MessageBox_80" sap2010:WorkflowViewState.IdRef="MessageBox_77" Selection="OK" Text="[out4]" Title="out4 before Deserialize Json4" />
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                            <ForEach x:TypeArguments="njl:JProperty" DisplayName="Classification_Split_ForEach_81" sap2010:WorkflowViewState.IdRef="ForEach`1_62" Values="[(JObject.Parse(jout1.ToString)).Properties()]">
                              <ActivityAction x:TypeArguments="njl:JProperty">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JProperty" Name="jout1item" />
                                </ActivityAction.Argument>
                                <Sequence DisplayName="Classification_Split_Sequence_82" sap2010:WorkflowViewState.IdRef="Sequence_159">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                                    <Variable x:TypeArguments="s:String[]" Name="CurVendorSupportingPages" />
                                    <Variable x:TypeArguments="x:String" Name="CurInvSupportingPages" />
                                  </Sequence.Variables>
                                  <Assign DisplayName="Classification_Split_Assign_CurInvoiceNumber_83" sap2010:WorkflowViewState.IdRef="Assign_535">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[jout1item.Name.ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_CurInvPageNoAll_84" sap2010:WorkflowViewState.IdRef="Assign_536">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvPageNoAll]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[String.Join(",",(jout1item.value))]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_CurInvPageNoStart_85" sap2010:WorkflowViewState.IdRef="Assign_537">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvPageNoStart]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[(jout1item.value)(0).tostring]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_CurVendorSupportingPages_86" sap2010:WorkflowViewState.IdRef="Assign_538">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="s:String[]">[CurVendorSupportingPages]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="s:String[]">[Joutitem.Value(2).ToObject(Of String())()]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_strCurVendorSupportingPages_87" sap2010:WorkflowViewState.IdRef="Assign_539">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[strCurVendorSupportingPages]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[String.Join(",", CurVendorSupportingPages.SelectMany(Function(s) s.Split(","c)).ToArray())]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_CurInvSupportingPages_88" sap2010:WorkflowViewState.IdRef="Assign_540">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvSupportingPages]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[String.Join(","c, CurVendorSupportingPages.Where(Function(p) p.Contains("_") AndAlso p.Split("_"c)(0).Trim() = CurInvPageNoStart.Trim()).Select(Function(p) p.Split("_"c)(1).Trim()))]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_CurInvSupportingPages_89" sap2010:WorkflowViewState.IdRef="Assign_541">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvSupportingPages]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[CurInvSupportingPages.Replace("''", "")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_NameForInvoice_90" sap2010:WorkflowViewState.IdRef="Assign_542">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[NameForInvoice]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["{"+CurInvoiceNumber+"~"+strVendName+"~"+CurInvPageNoAll+"~"+CurInvSupportingPages]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="Classification_Split_Assign_FinalPages_91" sap2010:WorkflowViewState.IdRef="Assign_543">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[FinalPages]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(String.IsNullOrWhiteSpace(CurInvSupportingPages),
   CurInvPageNoAll,
   String.Join(","c, (CurInvPageNoAll + "," + CurInvSupportingPages).Split(","c).Select(Function(x) x.Trim()).Distinct())
)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <iad:CommentOut DisplayName="Classification_Split_CommentOut_92" sap2010:WorkflowViewState.IdRef="CommentOut_34">
                                    <iad:CommentOut.Activities>
                                      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_MessageBox_93" sap2010:WorkflowViewState.IdRef="MessageBox_78" Selection="OK" Text="[&quot;Invoice Number - &quot;+CurInvoiceNumber+Environment.NewLine+&quot;Start Page - &quot;+CurInvPageNoStart+Environment.NewLine+&quot;CurInvSupportingPages - &quot;+CurInvSupportingPages+Environment.NewLine+&quot;FinalPages - &quot;+FinalPages+Environment.NewLine+&quot;NameForInvoice - &quot;+NameForInvoice+Environment.NewLine+&quot;Final Pages for split Script- &quot;+FinalPages]" Title="Before Invoke Dictionary" />
                                    </iad:CommentOut.Activities>
                                  </iad:CommentOut>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_26" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[NameForInvoice]</InArgument>
                                    <InArgument x:TypeArguments="x:String">[FinalPages.replace(",","-")]</InArgument>
                                  </InvokeMethod>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_544">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[blnInvDictExists]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                          </Sequence>
                          <Sequence x:Key="NotInvoice" DisplayName="Classification_Split_Sequence_94" sap2010:WorkflowViewState.IdRef="Sequence_163">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_545">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strVendName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Joutitem.Name.Tostring]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_546">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">Not Invoice Others</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Switch x:TypeArguments="x:String" DisplayName="Classification_Split_Switch_95" Expression="[Joutitem.Value(3).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_29">
                              <Switch.Default>
                                <Sequence DisplayName="Classification_Split_Sequence_96" sap2010:WorkflowViewState.IdRef="Sequence_161">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="Page" />
                                    <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                                    <Variable x:TypeArguments="x:String" Name="base64string" />
                                    <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                                    <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                                    <Variable x:TypeArguments="x:String" Name="IonBody1" />
                                    <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                                    <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                                    <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                    <Variable x:TypeArguments="x:String" Name="out4" />
                                    <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                                    <Variable x:TypeArguments="x:String" Name="CurInvoiceNumber" />
                                    <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                                  </Sequence.Variables>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_547">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["Not Invoice Others"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_548">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[CurInvPageNoStart]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[String.Join(",", CType(Joutitem.Value(1), JArray).Select(Function(x) x.ToString()).ToArray())]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_549">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[NameForInvoice]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["{"+CurInvoiceNumber+"~"+strVendName+"-"+UnstructuredCounter.tostring+"~"+CurInvPageNoStart+"~"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_550">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[FinalPages]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[CurInvPageNoStart]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_551">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".pdf",NameForInvoice+".pdf")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(pdfFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Classification_Split_If_97" sap2010:WorkflowViewState.IdRef="If_74">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_552">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_27" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[NameForInvoice]</InArgument>
                                    <InArgument x:TypeArguments="x:String">[FinalPages.replace(",","-")]</InArgument>
                                  </InvokeMethod>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_553">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[blnInvDictExists]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </Switch.Default>
                              <Sequence x:Key="Unstructured Expense Document" DisplayName="Classification_Split_Sequence_98" sap2010:WorkflowViewState.IdRef="Sequence_162">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="x:String" Name="Page" />
                                  <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                                  <Variable x:TypeArguments="x:String" Name="base64string" />
                                  <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                                  <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                                  <Variable x:TypeArguments="x:String" Name="IonBody1" />
                                  <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                                  <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                                  <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                  <Variable x:TypeArguments="x:String" Name="out4" />
                                  <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                                  <Variable x:TypeArguments="x:String" Name="CurInvoiceNumber" />
                                  <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                                </Sequence.Variables>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_554">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">["Unstructured Expense Document"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_555">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[CurInvPageNoStart]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[String.Join(",", CType(Joutitem.Value(1), JArray).Select(Function(x) x.ToString()).ToArray())]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_556">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[NameForInvoice]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">["{"+CurInvoiceNumber+"~"+strVendName+"-"+UnstructuredCounter.tostring+"~"+CurInvPageNoStart+"~"]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_557">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[FinalPages]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[CurInvPageNoStart]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_28" MethodName="Add">
                                  <InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</InArgument>
                                  </InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="x:String">[NameForInvoice]</InArgument>
                                  <InArgument x:TypeArguments="x:String">[FinalPages.replace(",","-")]</InArgument>
                                </InvokeMethod>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_558">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Int32">[UnstructuredCounter]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Int32">[UnstructuredCounter+1]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_559">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Boolean">[blnInvDictExists]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </Sequence>
                            </Switch>
                          </Sequence>
                        </Switch>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <Switch x:TypeArguments="x:Boolean" DisplayName="Classification_Split_Switch_99" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_31">
                    <Sequence x:Key="True" DisplayName="Classification_Split_Sequence_100" sap2010:WorkflowViewState.IdRef="Sequence_165">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                        <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                      </Sequence.Variables>
                      <Assign DisplayName="Classification_Split_Assign_pdfFileName_101" sap2010:WorkflowViewState.IdRef="Assign_560">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Copy_102" sap2010:WorkflowViewState.IdRef="File_Copy_16" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                      <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Delete_103" sap2010:WorkflowViewState.IdRef="File_Delete_15" Source="[item]" />
                      <Assign DisplayName="Classification_Split_Assign_item_104" sap2010:WorkflowViewState.IdRef="Assign_561">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </Switch>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_562">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[strInvoiceExistsCondition]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[blnInvDictExists.ToString.ToLower+"-"+((invoicePageDictionary.count)&gt;1).Tostring.ToLower]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Switch x:TypeArguments="x:String" DisplayName="Classification_Split_Switch_105" Expression="[strInvoiceExistsCondition]" sap2010:WorkflowViewState.IdRef="Switch`1_32">
                    <Sequence x:Key="true-true" DisplayName="Classification_Split_Sequence_106" sap2010:WorkflowViewState.IdRef="Sequence_167">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Int32" Name="intPageCount" />
                        <Variable x:TypeArguments="scg:List(x:String)" Name="ListAllPages" />
                        <Variable x:TypeArguments="x:Int32" Name="intMergeCounter" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_563">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[intMergeCounter]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">1</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Directory_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Directory_Delete_107" sap2010:WorkflowViewState.IdRef="Directory_Delete_3" Source="[configurationFolder+ &quot;\OutlookDownloads\SplitMergeProcessing&quot;]" />
                      <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_Directory_Create_108" sap2010:WorkflowViewState.IdRef="Directory_Create_3" Name="SplitMergeProcessing" Target="[configurationFolder+ &quot;\OutlookDownloads&quot;]" />
                      <ias:PDF_ReturnPages ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_PDF_ReturnPages_109" FilePath="[item]" sap2010:WorkflowViewState.IdRef="PDF_ReturnPages_3" Pages="[intPageCount]" />
                      <ias:PDF_SplitDocuments ErrorCode="{x:Null}" SplitSize="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_PDF_SplitDocuments_110" FilePath="[item]" sap2010:WorkflowViewState.IdRef="PDF_SplitDocuments_4" OutputDirectory="[configurationFolder+ &quot;\OutlookDownloads\SplitMergeProcessing&quot;]" PageSelection="[String.Join(&quot;,&quot;, Enumerable.Range(1, intPageCount))]" SplitCriteria="PageNumber" />
                      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+ &quot;\OutlookDownloads\SplitMergeProcessing&quot;]" DisplayName="Classification_Split_Directory_GetFiles_111" FileType="PDF" Files="[ListAllPages]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_8" IncludeSubDir="True" />
                      <ForEach x:TypeArguments="scg:KeyValuePair(x:String, x:String)" DisplayName="Classification_Split_ForEach_112" sap2010:WorkflowViewState.IdRef="ForEach`1_64" Values="[invoicePageDictionary]">
                        <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, x:String)">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, x:String)" Name="SplitFile" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_166">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="scg:List(x:String)" Name="ListTargetPages" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="SubListMerge" />
                              <Variable x:TypeArguments="x:String" Name="strOutputPath" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_564">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[ListTargetPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)" xml:space="preserve">[((SplitFile.Value).Replace("-",",")).Split(","c).
                Select(Function(x) "Page Number " &amp; x.Trim() &amp; ".pdf").
                ToList()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_565">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[SubListMerge]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)" xml:space="preserve">[ListAllPages.
            Where(Function(f) ListTargetPages.
              Any(Function(t) System.IO.Path.GetFileName(f).EndsWith(t))).
            ToList()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_566">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strMergeFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_567">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strMergeFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetFileNamewithoutExtension(item)+"{"+System.DateTime.Now.ToString("ddMMyyyyhhmmss")+"_"+intMergeCounter.ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_29" MethodName="Add">
                              <InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[DictFileNames]</InArgument>
                              </InvokeMethod.TargetObject>
                              <InArgument x:TypeArguments="x:String">[strMergeFileName+".pdf"]</InArgument>
                              <InArgument x:TypeArguments="x:String">[Path.GetFileNamewithoutExtension(item)+"-"+SplitFile.Key+"-"+(SplitFile.Value)+".pdf"]</InArgument>
                            </InvokeMethod>
                            <ias:PDF_MergeDocuments ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_PDF_MergeDocuments_113" sap2010:WorkflowViewState.IdRef="PDF_MergeDocuments_3" InputFiles="[SubListMerge]" OutputFile="[strOutputPath]" TargetFileName="[strMergeFileName+&quot;.pdf&quot;]" TargetFilePath="[configurationFolder+&quot;\InProgress&quot;]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_568">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[intMergeCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[intMergeCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                    <Sequence x:Key="true-false" DisplayName="Classification_Split_Sequence_114" sap2010:WorkflowViewState.IdRef="Sequence_168">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_569">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".pdf",NameForInvoice+".pdf")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(pdfFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Classification_Split_If_115" sap2010:WorkflowViewState.IdRef="If_75">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_570">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_571">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strMergeFileName]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Path.GetFileNamewithoutExtension(item)+"{"+System.DateTime.Now.ToString("ddMMyyyyhhmmss")+"_1"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_30" MethodName="Add">
                        <InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[DictFileNames]</InArgument>
                        </InvokeMethod.TargetObject>
                        <InArgument x:TypeArguments="x:String">[strMergeFileName+".pdf"]</InArgument>
                        <InArgument x:TypeArguments="x:String">[pdfFileName]</InArgument>
                      </InvokeMethod>
                      <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Copy_116" sap2010:WorkflowViewState.IdRef="File_Copy_17" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" TargetFilename="[strMergeFileName+&quot;.pdf&quot;]" />
                      <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Delete_117" sap2010:WorkflowViewState.IdRef="File_Delete_16" Source="[item]" />
                    </Sequence>
                  </Switch>
                </Sequence>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_Append_Line_118" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;Error Occurred during Split Process for file: &quot;+Path.GetfileName(item)+Environment.NewLine+exception.message]" Source="[logFile]" />
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
          </ActivityAction>
        </ForEach>
      </Sequence>
      <Sequence x:Key="False" DisplayName="Classification_Split_Sequence_119" sap2010:WorkflowViewState.IdRef="Sequence_110">
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Classification_Split_Directory_GetFiles_120" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_4" IncludeSubDir="True" />
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" DisplayName="Classification_Split_Directory_GetFiles_121" FileType="All" Files="[ListClassificationData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_5" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="Classification_Split_ForEach_122" sap2010:WorkflowViewState.IdRef="ForEach`1_52" Values="[MasterDownloadedFiles]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="item" />
            </ActivityAction.Argument>
            <Sequence DisplayName="Classification_Split_Sequence_123" sap2010:WorkflowViewState.IdRef="Sequence_125">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_312">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListOCRData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CurrentClassificationFIle]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListClassificationData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Read_124" sap2010:WorkflowViewState.IdRef="File_Read_25" Source="[CurrentOCRFile]" Text="[OCRText]" />
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Read_125" sap2010:WorkflowViewState.IdRef="File_Read_26" Source="[CurrentClassificationFIle]" Text="[ClassificationText]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_416">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[values]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRText)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_417">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[Jout]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ClassificationText)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_311">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[noOfPages]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[values("_metadata")("NumberOfPages").ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="njl:JProperty" DisplayName="Classification_Split_ForEach_126" sap2010:WorkflowViewState.IdRef="ForEach`1_58" Values="[(JObject.Parse(jout.ToString)).Properties()]">
                <ActivityAction x:TypeArguments="njl:JProperty">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="njl:JProperty" Name="Joutitem" />
                  </ActivityAction.Argument>
                  <Sequence DisplayName="Classification_Split_Sequence_127" sap2010:WorkflowViewState.IdRef="Sequence_136">
                    <iad:CommentOut DisplayName="Classification_Split_CommentOut_128" sap2010:WorkflowViewState.IdRef="CommentOut_21">
                      <iad:CommentOut.Activities>
                        <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_MessageBox_129" sap2010:WorkflowViewState.IdRef="MessageBox_50" Selection="OK" Text="[Joutitem.tostring]" Title="Joutitem Value from Classification" />
                      </iad:CommentOut.Activities>
                    </iad:CommentOut>
                    <Switch x:TypeArguments="x:String" DisplayName="Classification_Split_Switch_130" Expression="[Joutitem.Value(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_23">
                      <Sequence x:Key="Invoice" DisplayName="Classification_Split_Sequence_131" sap2010:WorkflowViewState.IdRef="Sequence_134">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="Page" />
                          <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                          <Variable x:TypeArguments="x:String" Name="base64string" />
                          <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                          <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                          <Variable x:TypeArguments="x:String" Name="IonBody1" />
                          <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                          <Variable x:TypeArguments="njl:JToken" Name="out3" />
                          <Variable x:TypeArguments="x:String" Name="out4" />
                          <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_455">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Sequence x:Key="NotInvoice" DisplayName="Classification_Split_Sequence_132" sap2010:WorkflowViewState.IdRef="Sequence_143">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_456">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Not Invoice Others</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:String" DisplayName="Classification_Split_Switch_133" Expression="[Joutitem.Value(3).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_22">
                          <Sequence x:Key="Unstructured Expense Document" DisplayName="Classification_Split_Sequence_134" sap2010:WorkflowViewState.IdRef="Sequence_135">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="Page" />
                              <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                              <Variable x:TypeArguments="x:String" Name="base64string" />
                              <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                              <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                              <Variable x:TypeArguments="x:String" Name="IonBody1" />
                              <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                              <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                              <Variable x:TypeArguments="x:String" Name="out4" />
                              <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                              <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_436">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["Unstructured Expense Document"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                      </Sequence>
                    </Switch>
                  </Sequence>
                </ActivityAction>
              </ForEach>
              <Switch x:TypeArguments="x:Boolean" DisplayName="Classification_Split_Switch_135" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                <Sequence x:Key="True" DisplayName="Classification_Split_Sequence_136" sap2010:WorkflowViewState.IdRef="Sequence_141">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                    <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                  </Sequence.Variables>
                  <Assign DisplayName="Classification_Split_Assign_pdfFileName_137" sap2010:WorkflowViewState.IdRef="Assign_451">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Copy_138" sap2010:WorkflowViewState.IdRef="File_Copy_13" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                  <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Delete_139" sap2010:WorkflowViewState.IdRef="File_Delete_13" Source="[item]" />
                  <Assign DisplayName="Classification_Split_Assign_item_140" sap2010:WorkflowViewState.IdRef="Assign_452">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </Switch>
              <Sequence DisplayName="Classification_Split_Sequence_141" sap2010:WorkflowViewState.IdRef="Sequence_142">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_453">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".pdf","{"+CurInvoiceNumber+".pdf")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(pdfFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Classification_Split_If_142" sap2010:WorkflowViewState.IdRef="If_69">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_454">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
                <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Classification_Split_File_Copy_143" sap2010:WorkflowViewState.IdRef="File_Copy_14" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" TargetFilename="[pdfFileName]" />
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification_Split_File_Delete_144" sap2010:WorkflowViewState.IdRef="File_Delete_14" Source="[item]" />
              </Sequence>
            </Sequence>
          </ActivityAction>
        </ForEach>
      </Sequence>
    </Switch>
    <sads:DebugSymbol.Symbol>d2BDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXENsYXNzaWZpY2F0aW9uX1NwbGl0LnhhbWyKBFYDrwsOAgEBaTNpNgIBAnQFigMQAwHxBIsDBZIDDgMB7QSTAwWtCw4CAQN1B3WRAgMB/AV2B4kDEQMB8gSQA0qQA2wDAfAEjQNLjQNaAwHuBJMDYJMDrAECAQSUAwfpCRICAXvqCQesCxICAQZ1qQF1wgEDAf8FdVR1ZwMB/QV2nwF2uAEDAfoFewuHAxYDAfMElQMJlQOvAgMB6ASWAwmWA98CAwHjBJcDCegJEwIBfOsJCesJrwICAXbsCQnsCd8CAgFx7QkJqwsTAgEHfQ/7AhoDAfgEgwMTgwPMAgMB9ASVA9EBlQPgAQMB6wSVA1aVA5MBAwHpBJYD9gGWA5ACAwHmBJYDVpYDngEDAeQElwOXAZcDsAEDAeEEnAMN5gkYAgF96wnRAesJ4AECAXnrCVbrCZMBAgF37An2AewJkAICAXTsCVbsCZ4BAgFy7QmXAe0JsAECAW/yCQ2pCxgCAQiEARGEAcMBAwH1BYUBEYUBqgIDAe0FhgERhgH1AgMB5wWHARGHAc0BAwHjBYgBEfoCGgMB+QSDA58BgwO2AgMB9wSDA74CgwPJAgMB9QSeAxHaCRwDAYIB4gkV4gmyAgIBfvMJD/8JGAIBa4AKD4wKGAIBZ40KD40KwwECAWKOCg+OCuQBAgFdjwoPlgoYAgFZlwoPngoYAgFVnwoPpgoYAgFRpwoP8woZAgE69AoPjQsYAgEijgsPqAsaAgEJhAGyAYQBwAEDAfgFhAGeAYQBrAEDAfYFhQGdAoUBpwIDAfMFhQGhAYUBsQEDAfEFhQHOAYUB3gEDAe8FhQE9hQFjAwHuBYYBnwKGAa0CAwHrBYYBtQKGAfICAwHpBYYBqAGGAZMCAwHoBYcBoQGHAbQBAwHmBYcBvAGHAcoBAwHkBYgBY4gBeQMB+gSJARP5Ah4DAfwEpQMTrAMcAwHdBK0DE7kDHAMB2QS6AxPGAxwDAdUExwMTxwPHAQMB0ATIAxPIA+gBAwHLBMkDE9ADHAMBxwTRAxPYAxwDAcME2QMTtQgdAwGMArYIE88IHAMB9AHQCBPXCBwDAe4B2AgT2QkcAwGDAeIJoQHiCZwCAwGBAeIJpALiCa8CAgF/9Qk79QlLAgFsggo7ggpWAgFojQq1AY0KwAECAWWNCp0BjQqvAQIBY44KywGOCuEBAgFgjgqoAY4KxQECAV6UCjyUClMCAVyRCj2RCkUCAVqcCjycCl4CAViZCj2ZCkMCAVakCjqkCmkCAVShCjuhCkYCAVKnCqoBpwrZAQIBUKwKE/EKHgIBO/QKZfQKiAECASP1ChGMCxwCASWSCxGZCxoCAR6aCxGlCxYCARamCxGmC7ACAgENpwsRpwurAQIBCpUBFZwBHgMB3wWdARWmAR4DAdsFpwEVpwHfAQMB1gWoARWsARoDAc4FrQEVtAEeAwHJBbUBFbwBHgMBxAW9ARXyASYDAcMF8wEVyAIgAwGmBckCFckC5wEDAaEFygIV3QIvAwGZBd4CFeUCHgMBlQXmAhXtAh4DAZEF7gIV9QIeAwGMBfYCFfYC4QEDAYcF9wIV9wKeAwMBgQX4AhX4AuUBAwH9BKoDWKoDfAMB4ASnA1mnA3ADAd4ErwM/rwNPAwHaBLwDP7wDWgMB1gTHA7kBxwPEAQMB0wTHA6EBxwOzAQMB0QTIA88ByAPlAQMBzgTIA6wByAPJAQMBzATOA0DOA1cDAcoEywNBywNJAwHIBNYDQNYDYgMBxgTTA0HTA0cDAcQE2QOzAdkD4gEDAcIE3gMXswgiAwGNArYIabYIjAEDAfUBtwgVzgggAwH3AdUIPtUImwEDAfEB0gg/0ghaAwHvAdgIddgIkgEDAYQB2QgVsQkgAwGqAbIJFdgJIAMBhgGtChWxCiYCAU+yChXwCh4CATz6ChOBCxwCATaCCxOCC6ICAgEugwsTgwutAQIBK4QLE4sLHAIBJpcLPJcLgAECASGUCz2UC0oCAR+aCx+aC48BAgEXnAsVowseAgEZpgufAaYLsQECARSmC98BpguOAgIBEqYLzwGmC9cBAgEQpgueAqYLrQICAQ6nC6ABpwuoAQIBC5oBQJoBbwMB4gWXAUGXAUwDAeAFowEbowFKAwHeBZ8BQZ8BTQMB3AWnAagBpwG4AQMB2QWnAb4BpwHcAQMB1wWoASOoATMDAc8FqgEZqgHbAQMB0QWyAUCyAWMDAcwFrwFBrwFNAwHKBboBQLoBagMBxwW3AUG3AVQDAcUF9AEX+wEgAwG9BfwBF4MCIAMBuAWEAheLAiADAbQFjAIXkwIgAwGwBZQCF5sCIAMBrAWcAhezAigDAasFtAIXxwIsAwGnBckC2QHJAuQBAwGkBckCtgHJAssBAwGiBcoChQLKAqMCAwGgBcoCzALKAt0CAwGeBcoCrQLKAr4CAwGcBcoC4gLKApoDAwGaBeMCQuMCagMBmAXgAkPgAkkDAZYF6wJA6wJrAwGUBegCQegCRwMBkgXzAkDzAnsDAY8F8AJB8AJHAwGNBfYCzQH2At4BAwGKBfYCtwH2Ar8BAwGIBfcCswL3AssCAwGFBfcC0wL3ApsDAwGDBfcCvAH3AqcCAwGCBfgCsQH4AsIBAwGABfgCygH4AuIBAwH+BOMDGecDKgMBwQToAxmyCCIDAY4CvAgXwwggAwGIAsQIF8QIpgIDAYACxQgXxQixAQMB/QHGCBfNCCADAfgB3wgX5gggAwHqAecIF+cIggIDAecB6AgX6AigAgMB4wHpCBfpCN4BAwHeAeoIF+oIpQMDAdgB6wgX6wjeAgMB0wHsCBewCSEDAasBswkXugkgAwGmAbsJF8YJHAMBngHHCRfOCSADAZoBzwkX1QkmAwGTAdYJF9YJzAIDAYoB1wkX1wmxAQMBhwGyCmGyCn8CAT2zChfLCiICAUrMChfvCiICAT7/Cj7/CnICATn8Cj/8CkwCATeCC6EBgguzAQIBNIIL4QGCC4ACAgEzggvRAYIL2QECATGCC5ACggufAgIBL4MLogGDC6oBAgEsiQs+iQtgAgEphgs/hgtFAgEnoQtAoQufAQIBHJ4LQZ4LTgIBGqoBygGqAdgBAwHUBaoBpgGqAcQBAwHSBfkBQvkBdQMBwAX2AUP2AVIDAb4FgQJCgQJjAwG7Bf4BQ/4BUgMBuQWJAkKJAosBAwG3BYYCQ4YCVAMBtQWRAkKRAosBAwGzBY4CQ44CUgMBsQWZAkKZAr8BAwGvBZYCQ5YCSgMBrQW0AtwQtALnEAMBqQW0AvQBtALWEAMBqAXoA2XoA4MBAwGPAukDG/8GJgMB7gKABxuxCCYDAZACwQhCwQh2AwGLAr4IQ74IUAMBiQLECKUBxAi3AQMBhgLECOUBxAiEAgMBhQLECNUBxAjdAQMBgwLECJQCxAijAgMBgQLFCKYBxQiuAQMB/gHLCELLCGQDAfsByAhDyAhJAwH5AeQIQeQIQgMB7QHhCELhCFMDAesB5wi0AecI/wEDAegB6AjnAegInQIDAeUB6AjJAegI3wEDAeQB6QjLAekI2wEDAeEB6QiIAekIkAEDAd8B6gjsAeoItwIDAdwB6gjGAuoIhwMDAdsB6gicAeoIpAEDAdkB6wj/AesIjwIDAdYB6whk6wivAQMB1AHsCKwB7AjFAQMB0QHxCBuuCSYDAawBuAlCuAmAAQMBqQG1CUO1CVADAacBuwkluwmVAQMBnwG9CRvECSQDAaEBzAlCzAmiAQMBnQHJCUPJCVUDAZsB1AlA1AlNAwGYAdMJQNMJWQMBlgHRCVzRCWsDAZQB1gmlAdYJtwEDAZEB1gnlAdYJlAIDAY8B1gnVAdYJ3QEDAY0B1gmkAtYJyQIDAYsB1wmmAdcJrgEDAYgBwQoZygoiAgFLzQoZ1AoiAgFG1QoZ7goiAgE/9wMd/gMmAwG9BP8DHbYEKAMBqwS3BB2+BCYDAacEvwQdvwSJAgMBpATABB3ABNMCAwGfBMEEHcEEzQEDAZwEwgQdwgTVAQMBmATDBB3DBNgBAwGUBMQEHcQE2AEDAZAExQQdxQTpAQMBiwTGBB3GBOYBAwGGBMcEHccEqgEDAYUEyAQd0QQmAwGBBNIEHdIE4gEDAfwD0wQd1wQiAwH0A9gEHdgE1wEDAe8D2QQd4AQmAwHqA+EEHegEJgMB5QPpBB2YBS4DAeQDmQUd2AUoAwHIA9kFHdkF9wEDAcMD2gUd7QU3AwG7A+4FHfUFJgMBtwP2BR39BSYDAbMD/gUdhQYmAwGuA4YGHYoGLgMBrQOLBh2LBuwBAwGoA4wGHZEGLgMBpwOSBh3+BicDAe8CgQcdiAcmAwHqAokHHZAHJgMB5gKRBx2wCCYDAZEC9wgdgAkmAwHNAYEJHYsJJgMByQGMCR2VCSYDAcUBlgkdnQkmAwHBAZ4JHaQJLAMBuwGlCR2lCeoCAwGyAaYJHa0JJgMBrQHCCUbCCaUBAwGkAb8JR78JVAMBogHHCh/HCk4CAU7DCkXDClcCAUzSCkTSClYCAUnPCkXPClcCAUfVCpgB1Qq2AQIBQNYKG+0KJgIBQfwDSPwDYAMBwAT5A0n5A1YDAb4EgAQfhwQoAwG5BIgEH48EKAMBtQSQBB+XBCgDAbEEmAQfpgQoAwGtBKcEH7UEMAMBrAS8BEi8BGcDAaoEuQRJuQRPAwGoBL8E2QG/BPIBAwGmBL8EggK/BIYCAwGlBMAEpgHABJ4CAwGjBMAEywLABNACAwGiBMAEqQLABKwCAwGhBMAEuALABLsCAwGgBMEEuAHBBLsBAwGeBMEExwHBBMoBAwGdBMIEwAHCBMMBAwGbBMIEsQHCBLUBAwGaBMIEzwHCBNIBAwGZBMMEwAHDBMMBAwGXBMMEsQHDBLUBAwGWBMMEzwHDBNUBAwGVBMQEwAHEBMMBAwGTBMQEsQHEBLUBAwGSBMQEzwHEBNUBAwGRBMUE0AHFBNMBAwGPBMUE3wHFBOYBAwGOBMUEswHFBMUBAwGMBMYES8YEWwMBiQTGBKABxgSyAQMBhwTOBCPOBFIDAYQEygRJygRaAwGCBNIEsAHSBMQBAwH/A9IEygHSBN8BAwH9A9MEK9MEPwMB9QPVBCHVBOABAwH3A9gEwAHYBNQBAwHyA9gEqwHYBLoBAwHwA94ESN4EawMB7QPbBEnbBFUDAesD5gRI5gRyAwHoA+MESeMEXAMB5gOaBR+hBSgDAd4DogUfqQUoAwHZA6oFH7EFKAMB1QOyBR+5BSgDAdEDugUfwQUoAwHNA8IFH9cFNAMByQPZBegB2QX0AQMBxgPZBcAB2QXaAQMBxAPaBY4C2gWoAgMBwgPaBdIC2gXjAgMBwAPaBbIC2gXEAgMBvgPaBegC2gWgAwMBvAPzBUrzBXMDAboD8AVL8AVRAwG4A/sFSPsFcwMBtgP4BUn4BU8DAbQDgwZIgwaDAQMBsQOABkmABk8DAa8DiwbYAYsG6QEDAasDiwbBAYsGygEDAakDkga7AZIG6wEDAaYDlwYh/AYsAwHwAoYHSIYHYAMB7QKDB0mDB1YDAesCjgdIjgdaAwHpAosHSYsHWwMB5wKRB3eRB5UBAwGSAugHH68IKgMBwAKTByHmBywDAZMC+QhT+QhkAwHOAYMJU4MJYQMBygGSCSOSCVIDAcgBjglJjglbAwHGAZsJSJsJwAEDAcQBmAlJmAlbAwHCAaMJRqMJnQEDAcABoglGoglfAwG+AaAJYqAJcQMBvAGlCeIBpQnzAQMBuQGlCcYBpQnWAQMBtwGlCbgCpQnnAgMBtQGlCYMCpQmoAgMBswGrCUerCVoDAbABqAlIqAlZAwGuAeUKHewKJgIBQoUETIUEYwMBvASCBE2CBFsDAboEjQRKjQSfAQMBuASKBEuKBFgDAbYElQRMlQSMAQMBtASSBE2SBF8DAbIEmgRLmgRcAwGuBNUEygHVBN0BAwH6A9UErwHVBMQBAwH4A58FSp8FiAEDAeEDnAVLnAVfAwHfA6cFSqcFcAMB3AOkBUukBV8DAdoDrwVKrwWYAQMB2AOsBUusBVwDAdYDtwVKtwWYAQMB1AO0BUu0BV8DAdIDvwVKvwXQAQMB0AO8BUu8BVIDAc4DwgWzEMIFvxADAcsDwgXFAcIFrRADAcoDnQYjpAYsAwGiA6UGI6wGLAMBngOtBiO0BiwDAZoDtQYjvAYsAwGWA70GI8QGLAMBkgPFBiPMBiwDAY4DzQYj1AYsAwGJA9UGI9wGLAMBgQPdBiPnBiwDAf0C6AYj7AY0AwH8Au0GI/MGMgMB9QL0BiP7BiwDAfEC+Ach/wcqAwHiAoAIIYcIKgMB3gKICCGPCCoDAdYCkAghlwgqAwHRApgIIZ4IMAMBygKfCCGmCCoDAcUCpwghrggqAwHBAqMHI6oHLAMBvAKrByOyBywDAbgCswcjugcsAwGwArsHI8IHLAMBqwLDByPKBywDAacCywcj1gcoAwGfAtcHI90HMgMBmALeByPlBywDAZQC6gpI6gppAgFF5wpJ5wpbAgFDogZOogZnAwGlA58GT58GYQMBowOqBk6qBnIDAaEDpwZPpwZgAwGfA7IGTrIGbQMBnQOvBk+vBmIDAZsDugZQugZ7AwGZA7cGUbcGawMBlwPCBk7CBqoBAwGVA78GT78GbAMBkwPKBk7KBoUCAwGRA8cGT8cGZgMBjwPSBk7SBncDAYwDzwZPzwZmAwGKA9oGTtoGogEDAYQD1wZP1wZfAwGCA98GT98GWwMB/gLyBkzyBmkDAfoC8QZM8QZcAwH4Au8GaO8GfwMB9gL5Bk/5BlMDAfQC9gZQ9gZiAwHyAv0HTP0HbQMB5QL6B036B18DAeMChQhMhQirAQMB4QKCCE2CCGADAd8CjQhMjQitAQMB2QKKCE2KCF0DAdcClQhMlQhfAwHUApIITZIIWQMB0gKdCEqdCGcDAc8CnAhKnAhaAwHNApoIZpoIfQMBywKkCEukCGIDAcgCoQhMoQhhAwHGAqwITawIUQMBxAKpCE6pCGADAcICqAdOqAdkAwG/AqUHT6UHYQMBvQKwB06wB60BAwG7Aq0HT60HYgMBuQK4B064B68BAwGzArUHT7UHXwMBsQLAB07AB2EDAa4CvQdPvQdbAwGsAsgHTsgHjAEDAaoCxQdPxQdcAwGoAssHMcsHoQEDAaACzQcn1AcwAwGiAtwHTNwHaQMBnQLbB0zbB1wDAZsC2Qdo2Qd/AwGZAuMHT+MHUwMBlwLgB1DgB2IDAZUC0gdS0gexAQMBpQLPB1PPB2ADAaMC</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="548,22" />
      <sap2010:ViewStateData Id="File_Read_4" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="DocumentOC_2" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="File_Create_3" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Read_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_468" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_469" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_22" sap:VirtualizedContainerService.HintSize="464,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_480" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_481" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_482" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_483" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_484" sap:VirtualizedContainerService.HintSize="242,59.3333333333333" />
      <sap2010:ViewStateData Id="Template_Apply_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_25" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_16" sap:VirtualizedContainerService.HintSize="242,46.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_149" sap:VirtualizedContainerService.HintSize="464,778.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_9" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_10" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Create_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="486,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_3" sap:VirtualizedContainerService.HintSize="476.666666666667,145.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="498.666666666667,517.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="503.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="517.333333333333,755.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_33" sap:VirtualizedContainerService.HintSize="548,908">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="570,1094">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_495" sap:VirtualizedContainerService.HintSize="570,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_2" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_509" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_510" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_511" sap:VirtualizedContainerService.HintSize="476.666666666667,65.3333333333333" />
      <sap2010:ViewStateData Id="File_Read_28" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="File_Read_29" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_512" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_513" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="MessageBox_74" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_29" sap:VirtualizedContainerService.HintSize="200,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_514" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_515" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_516" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_517" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_518" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_519" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_30" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_156" sap:VirtualizedContainerService.HintSize="586.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_520" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="OpenBrowser_9" sap:VirtualizedContainerService.HintSize="586.666666666667,58" />
      <sap2010:ViewStateData Id="NavigateTo_9" sap:VirtualizedContainerService.HintSize="586.666666666667,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_9" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_25" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_26" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_27" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="ScreenShot_9" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="FileToBase64_9" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="CloseBrowser_9" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_521" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Path_Validate_4" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="File_Read_30" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_73" sap:VirtualizedContainerService.HintSize="586.666666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Read_31" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_522" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_523" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_524" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_525" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_526" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_17" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_157" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_31" sap:VirtualizedContainerService.HintSize="586.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_527" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_528" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_529" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_530" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_531" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_158" sap:VirtualizedContainerService.HintSize="586.666666666667,656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_26" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_12" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_532" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_533" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_534" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="MessageBox_75" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_32" sap:VirtualizedContainerService.HintSize="586.666666666667,118" />
      <sap2010:ViewStateData Id="DeserializeJSON_27" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="MessageBox_76" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="MessageBox_77" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_33" sap:VirtualizedContainerService.HintSize="586.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_535" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_536" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_537" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_538" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_539" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_540" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_541" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_542" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_543" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="MessageBox_78" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_34" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_26" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_544" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_159" sap:VirtualizedContainerService.HintSize="264,1379.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_62" sap:VirtualizedContainerService.HintSize="586.666666666667,1532">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_160" sap:VirtualizedContainerService.HintSize="608.666666666667,5344">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_545" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_546" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_547" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_548" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_549" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_550" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_551" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_552" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_74" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_27" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_553" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_161" sap:VirtualizedContainerService.HintSize="264,962.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_554" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_555" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_556" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_557" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_28" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_558" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_559" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_162" sap:VirtualizedContainerService.HintSize="264,867.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_29" sap:VirtualizedContainerService.HintSize="476.666666666667,145.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_163" sap:VirtualizedContainerService.HintSize="498.666666666667,473.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_30" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_164" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_63" sap:VirtualizedContainerService.HintSize="476.666666666667,212.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_560" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_561" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_165" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_31" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_562" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_563" sap:VirtualizedContainerService.HintSize="294.666666666667,62" />
      <sap2010:ViewStateData Id="Directory_Delete_3" sap:VirtualizedContainerService.HintSize="294.666666666667,22" />
      <sap2010:ViewStateData Id="Directory_Create_3" sap:VirtualizedContainerService.HintSize="294.666666666667,22" />
      <sap2010:ViewStateData Id="PDF_ReturnPages_3" sap:VirtualizedContainerService.HintSize="294.666666666667,22" />
      <sap2010:ViewStateData Id="PDF_SplitDocuments_4" sap:VirtualizedContainerService.HintSize="294.666666666667,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_8" sap:VirtualizedContainerService.HintSize="294.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_564" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_565" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_566" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_567" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_29" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="PDF_MergeDocuments_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_568" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_166" sap:VirtualizedContainerService.HintSize="264,836.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_64" sap:VirtualizedContainerService.HintSize="294.666666666667,989.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_167" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_569" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_570" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_75" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_571" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_30" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="File_Copy_17" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_168" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_32" sap:VirtualizedContainerService.HintSize="476.666666666667,274.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_169" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_40" sap:VirtualizedContainerService.HintSize="284.666666666667,212.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_109" sap:VirtualizedContainerService.HintSize="306.666666666667,460.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_4" sap:VirtualizedContainerService.HintSize="582,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_5" sap:VirtualizedContainerService.HintSize="582,22" />
      <sap2010:ViewStateData Id="Assign_312" sap:VirtualizedContainerService.HintSize="529.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="529.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="File_Read_25" sap:VirtualizedContainerService.HintSize="529.333333333333,22" />
      <sap2010:ViewStateData Id="File_Read_26" sap:VirtualizedContainerService.HintSize="529.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_416" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_417" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="529.333333333333,62" />
      <sap2010:ViewStateData Id="MessageBox_50" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_21" sap:VirtualizedContainerService.HintSize="476.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_455" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="264,183.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_456" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_436" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_22" sap:VirtualizedContainerService.HintSize="476.666666666667,145.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="498.666666666667,472.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_23" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="498.666666666667,390.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_58" sap:VirtualizedContainerService.HintSize="529.333333333333,543.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_451" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_452" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="529.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_453" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_454" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_69" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Copy_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="529.333333333333,402.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="551.333333333333,1843.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_52" sap:VirtualizedContainerService.HintSize="582,1996">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="604,2244">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_27" sap:VirtualizedContainerService.HintSize="570,675.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="592,2035.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="632,2475.33333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
