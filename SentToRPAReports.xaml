﻿<?xml version="1.0" encoding="utf-8"?>
<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities" xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI" xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys" xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities" xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json" xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger" xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation" xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation" xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib" xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentName" Type="InArgument(x:String)" />
    <x:Property Name="logicalId" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="RPA_Process_ID" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="InArgument(x:String)" />
    <x:Property Name="Status_Display" Type="InArgument(x:String)" />
    <x:Property Name="Comments" Type="InArgument(x:String)" />
    <x:Property Name="Last_Run_Time" Type="InArgument(x:String)" />
    <x:Property Name="Process_Type" Type="InArgument(x:String)" />
    <x:Property Name="Name" Type="InArgument(x:String)" />
    <x:Property Name="ERP" Type="InArgument(x:String)" />
    <x:Property Name="Variation_ID" Type="InArgument(x:String)" />
    <x:Property Name="Updated_By" Type="InArgument(x:String)" />
    <x:Property Name="Additional1" Type="InArgument(x:String)" />
    <x:Property Name="Additional2" Type="InArgument(x:String)" />
    <x:Property Name="failureCount" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="Email_Reciever" Type="InArgument(x:String)" />
    <x:Property Name="Email_RecievedTime" Type="InArgument(x:String)" />
    <x:Property Name="Exceution_StartTime" Type="InArgument(x:String)" />
    <x:Property Name="FileName" Type="InArgument(x:String)" />
    <x:Property Name="Email_Subject" Type="InArgument(x:String)" />
    <x:Property Name="Email_Sender" Type="InArgument(x:String)" />
    <x:Property Name="EmailID" Type="InArgument(x:String)" />
    <x:Property Name="Execution_EndTime" Type="InArgument(x:String)" />
    <x:Property Name="ProcessId" Type="InArgument(x:String)" />
    <x:Property Name="ProcessFilename" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="SentToRPAReports_Sequence_1" sap2010:WorkflowViewState.IdRef="Sequence_7">
    <Sequence.Variables>
      <Variable x:TypeArguments="njl:JToken" Name="requestToken" />
      <Variable x:TypeArguments="x:String" Name="requestStr" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="responseObject" />
      <Variable x:TypeArguments="x:Int32" Name="responseStatus" />
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JToken" Name="jout1" />
      <Variable x:TypeArguments="x:String" Name="varJtoken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
      <Variable x:TypeArguments="njl:JToken" Name="uout" />
      <Variable x:TypeArguments="x:String" Name="userID" />
      <Variable x:TypeArguments="x:String" Name="createdBy" />
    </Sequence.Variables>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SentToRPAReports_IONAPIRequestWizard_2" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" PostData="[&quot;&quot;]" Response="[resp]" Url="[tenantID+&quot;ifsservice/usermgt/v2/users/me&quot;]" />
    <Assign DisplayName="SentToRPAReports_Assign_uout_3" sap2010:WorkflowViewState.IdRef="Assign_7">
      <Assign.To>
        <OutArgument x:TypeArguments="njl:JToken">[uout]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(resp.ReadAsText)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="SentToRPAReports_Assign_createdBy_4" sap2010:WorkflowViewState.IdRef="Assign_8">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[createdBy]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[(uout("response"))("userlist")(0)("userName").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToRPAReports_Template_Apply_5" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="[&quot;{\&quot;&quot;EmailID\&quot;&quot;:\&quot;&quot;{{%EmailID%}}\&quot;&quot;, \&quot;&quot;Email_Sender\&quot;&quot;:\&quot;&quot;{{%Email_Sender%}}\&quot;&quot;, \&quot;&quot;Email_Reciever\&quot;&quot;:\&quot;&quot;{{%Email_Reciever%}}\&quot;&quot;, \&quot;&quot;Email_RecievedTime\&quot;&quot;:\&quot;&quot;{{%Email_RecievedTime%}}\&quot;&quot;, \&quot;&quot;Email_Subject\&quot;&quot;:\&quot;&quot;{{%Email_Subject%}}\&quot;&quot;, \&quot;&quot;FileName\&quot;&quot;:\&quot;&quot;{{%FileName%}}\&quot;&quot;, \&quot;&quot;Exceution_StartTime\&quot;&quot;:\&quot;&quot;{{%Exceution_StartTime%}}\&quot;&quot;, \&quot;&quot;Execution_EndTime\&quot;&quot;:\&quot;&quot;{{%Execution_EndTime%}}\&quot;&quot;, \&quot;&quot;Status\&quot;&quot;:\&quot;&quot;{{%Status%}}\&quot;&quot;, \&quot;&quot;Comments\&quot;&quot;:\&quot;&quot;{{%Comments%}}\&quot;&quot;, \&quot;&quot;ProcessId\&quot;&quot;:\&quot;&quot;{{%ProcessId%}}\&quot;&quot;, \&quot;&quot;ProcessFilename\&quot;&quot;:\&quot;&quot;{{%ProcessFilename%}}\&quot;&quot;}&quot;]" Text="[value]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="16">
            <x:String>Status</x:String>
            <x:String>Comments</x:String>
            <x:String>Email_Subject</x:String>
            <x:String>Email_Sender</x:String>
            <x:String>EmailID</x:String>
            <x:String>FileName</x:String>
            <x:String>Execution_EndTime</x:String>
            <x:String>Exceution_StartTime</x:String>
            <x:String>Email_RecievedTime</x:String>
            <x:String>Email_Reciever</x:String>
            <x:String>ProcessId</x:String>
            <x:String>ProcessFilename</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="16">
            <x:String>Status</x:String>
            <x:String>Comments</x:String>
            <x:String>Email_Subject</x:String>
            <x:String>Email_Sender</x:String>
            <x:String>EmailID</x:String>
            <x:String>FileName</x:String>
            <x:String>Execution_EndTime</x:String>
            <x:String>Exceution_StartTime</x:String>
            <x:String>Email_RecievedTime</x:String>
            <x:String>Email_Reciever</x:String>
            <x:String>ProcessId</x:String>
            <x:String>ProcessFilename</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToRPAReports_Template_Apply_6" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="[&quot;{  &quot;&quot;documentName&quot;&quot;: &quot;&quot;{{%documentName%}}&quot;&quot;,  &quot;&quot;messageId&quot;&quot;: &quot;&quot;msg#1234555&quot;&quot;, &quot;&quot;fromLogicalId&quot;&quot;: &quot;&quot;lid://{{%logicalId%}}&quot;&quot;, &quot;&quot;toLogicalId&quot;&quot;: &quot;&quot;lid://default&quot;&quot;,  &quot;&quot;document&quot;&quot;: {     &quot;&quot;value&quot;&quot;: &quot;&quot;{{%datalakeAttributes%}}&quot;&quot;,  &quot;&quot;encoding&quot;&quot;: &quot;&quot;NONE&quot;&quot;,  &quot;&quot;characterSet&quot;&quot;: &quot;&quot;UTF-8&quot;&quot; } }&quot;]" Text="[requestStr]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>documentName</x:String>
            <x:String>logicalId</x:String>
            <x:String>datalakeAttributes</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>documentName</x:String>
            <x:String>logicalId</x:String>
            <x:String>value</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToRPAReports_DeserializeJSON_7" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[jout]" JTokenString="[requestStr]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" QueryParameters="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="SentToRPAReports_IONAPIRequestWizard_8" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" PostData="[jout]" Response="[responseObject]" ResponseCode="[responseStatus]" Url="[imsAPIUrl]" />
    <If Condition="[responseStatus= 200]" DisplayName="SentToRPAReports_If_9" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence DisplayName="SentToRPAReports_Sequence_10" sap2010:WorkflowViewState.IdRef="Sequence_6">
          <Sequence.Variables>
            <Variable x:TypeArguments="njl:JToken" Name="reponseToken" />
          </Sequence.Variables>
          <Assign DisplayName="SentToRPAReports_Assign_reponseToken_11" sap2010:WorkflowViewState.IdRef="Assign_9">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[reponseToken]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[responseObject.readasjson]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[reponseToken.tostring.Contains(&quot;status&quot;) and reponseToken(&quot;status&quot;).tostring=&quot;OK&quot;]" DisplayName="SentToRPAReports_If_12" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToRPAReports_Append_Line_13" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[&quot;Storing Statistics data pushed to RPA_EmailReport.&quot;+Environment.NewLine+&quot;***********************##### END OF FILE #####***********************&quot;]" Source="[logFile]" />
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SentToRPAReports_Append_Line_14" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[&quot;Failed to push Storing Statistics data to RPA_EmailReport.&quot;+Environment.NewLine+&quot;***********************##### END OF FILE #####***********************&quot;]" Source="[logFile]" />
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFNlbnRUb1JQQVJlcG9ydHMueGFtbCddA8sBDgIBAWwFbJsDAgExbQV0DgIBLXUFfA4CASl9BZ4BGgIBJZ8BBa4BGgIBIa8BBa8BzQECARywAQWwAfwCAgEUsQEFyQEKAgECbLoCbMoCAgE2bNQCbNwCAgE0bOECbJgDAgEycjJyUQIBMG8zbzkCAS56MHpoAgEsdzF3PAIBKn25CX3CCQIBJ32jAX2zCQIBJp8B/AWfAYoGAgEjnwGjAZ8B9gUCASKvAbwBrwHKAQIBH68BpgGvAa4BAgEdsAGjArABqwICARuwAdUCsAHnAgIBGbABtQKwAccCAgEXsAHsArAB+QICARWxAROxASoCAQOzAQnHARQCAQW3AQu+ARQCARC/AQvGARACAQa8ATi8AVMCARO5ATm5AUcCARG/ARm/AYwBAgEHwQEPwQHZAgIBDMQBD8QB4QICAQjBAZsBwQHDAgIBD8EBywLBAdYCAgENxAGbAcQBywICAQvEAdMCxAHeAgIBCQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="486,440">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="612,594" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="634,1232">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="674,1312" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>
